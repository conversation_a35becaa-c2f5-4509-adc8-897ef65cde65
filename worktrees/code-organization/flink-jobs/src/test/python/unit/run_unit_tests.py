#!/usr/bin/env python3
"""
Unit test runner for Python order enrichment models.
"""

import sys
import os
import unittest
import time

# Add the source directory to Python path
current_dir = os.path.dirname(__file__)
source_dir = os.path.join(current_dir, '../../../../main/python')
sys.path.insert(0, os.path.abspath(source_dir))


def run_unit_tests():
    """Run all unit tests."""
    print("🧪 Running Python Order Enrichment Unit Tests")
    print("=" * 50)
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    start_time = time.time()
    result = runner.run(suite)
    elapsed_time = time.time() - start_time
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  Tests run: {result.testsRun}")
    print(f"  Failures: {len(result.failures)}")
    print(f"  Errors: {len(result.errors)}")
    print(f"  Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"  Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"  Execution time: {elapsed_time:.2f} seconds")
    
    if result.wasSuccessful():
        print("🎉 All unit tests passed!")
        return True
    else:
        print("❌ Some unit tests failed!")
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
        return False


if __name__ == "__main__":
    success = run_unit_tests()
    sys.exit(0 if success else 1)
