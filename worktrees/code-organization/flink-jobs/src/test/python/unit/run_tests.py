#!/usr/bin/env python3
"""
Test runner for the Python Flink enrichment job tests.

This script provides a convenient way to run different types of tests
with appropriate configurations and reporting.
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path


def setup_python_path():
    """Add the source directory to Python path."""
    current_dir = Path(__file__).parent
    source_dir = current_dir / "../../../main/python"
    source_dir_abs = source_dir.resolve()
    
    if str(source_dir_abs) not in sys.path:
        sys.path.insert(0, str(source_dir_abs))
    
    # Also set PYTHONPATH environment variable for subprocess calls
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    if current_pythonpath:
        os.environ['PYTHONPATH'] = f"{source_dir_abs}:{current_pythonpath}"
    else:
        os.environ['PYTHONPATH'] = str(source_dir_abs)


def run_unit_tests():
    """Run unit tests only."""
    print("🧪 Running unit tests...")
    cmd = [
        sys.executable, "-m", "pytest",
        "-m", "not integration",
        "--tb=short",
        "-v"
    ]
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def run_integration_tests():
    """Run integration tests only."""
    print("🔗 Running integration tests...")
    cmd = [
        sys.executable, "-m", "pytest",
        "-m", "integration",
        "--tb=short",
        "-v"
    ]
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def run_all_tests():
    """Run all tests."""
    print("🚀 Running all tests...")
    cmd = [
        sys.executable, "-m", "pytest",
        "--tb=short",
        "-v"
    ]
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def run_specific_test(test_path):
    """Run a specific test file or test function."""
    print(f"🎯 Running specific test: {test_path}")
    cmd = [
        sys.executable, "-m", "pytest",
        test_path,
        "--tb=short",
        "-v"
    ]
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def run_coverage_report():
    """Run tests with coverage reporting."""
    print("📊 Running tests with coverage...")
    
    # Check if pytest-cov is available
    try:
        import pytest_cov
    except ImportError:
        print("❌ pytest-cov not installed. Install with: pip install pytest-cov")
        return subprocess.CompletedProcess([], 1)
    
    cmd = [
        sys.executable, "-m", "pytest",
        "--cov=com.goodrx.flink.enrichment",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--tb=short",
        "-v"
    ]
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description="Run Python Flink enrichment job tests")
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "all", "coverage"],
        nargs="?",
        default="all",
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "--test",
        help="Run a specific test file or test function"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available test files"
    )
    
    args = parser.parse_args()
    
    # Setup Python path
    setup_python_path()
    
    if args.list:
        print("📋 Available test files:")
        test_dir = Path(__file__).parent
        for test_file in test_dir.rglob("test_*.py"):
            rel_path = test_file.relative_to(test_dir)
            print(f"  {rel_path}")
        return
    
    if args.test:
        result = run_specific_test(args.test)
    elif args.test_type == "unit":
        result = run_unit_tests()
    elif args.test_type == "integration":
        result = run_integration_tests()
    elif args.test_type == "coverage":
        result = run_coverage_report()
    else:  # all
        result = run_all_tests()
    
    if result.returncode == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(result.returncode)


if __name__ == "__main__":
    main()
