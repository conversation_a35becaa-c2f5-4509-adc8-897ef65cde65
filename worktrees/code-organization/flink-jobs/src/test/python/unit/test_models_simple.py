#!/usr/bin/env python3
"""
Simple unit tests for the order enrichment models (no external dependencies).
"""

import sys
import os
import json
import time

# Add the source directory to Python path
current_dir = os.path.dirname(__file__)
source_dir = os.path.join(current_dir, '../../../../main/python')
sys.path.insert(0, os.path.abspath(source_dir))


def test_order_event():
    """Test OrderEvent model functionality."""
    print("🧪 Testing OrderEvent model...")
    
    try:
        from com.goodrx.flink.enrichment.model.order_event import OrderEvent
        
        # Test 1: Basic creation
        order = OrderEvent(
            order_id='test-order-001',
            customer_id='customer-123',
            product_id='product-456',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        assert order.order_id == 'test-order-001'
        assert order.quantity == 2
        print("  ✅ Basic creation works")
        
        # Test 2: from_dict
        data = {
            'orderId': 'order-002',
            'customerId': 'customer-789',
            'productId': 'product-012',
            'quantity': 1,
            'price': 49.99,
            'eventTimestamp': 1640995260000
        }
        order2 = OrderEvent.from_dict(data)
        assert order2.order_id == 'order-002'
        assert order2.price == 49.99
        print("  ✅ from_dict works")
        
        # Test 3: to_dict
        result_dict = order2.to_dict()
        assert result_dict == data
        print("  ✅ to_dict works")
        
        # Test 4: JSON serialization
        json_str = order2.to_json()
        parsed = json.loads(json_str)
        assert parsed['orderId'] == 'order-002'
        print("  ✅ JSON serialization works")
        
        # Test 5: from_json
        order3 = OrderEvent.from_json(json_str)
        assert order3.order_id == order2.order_id
        assert order3.price == order2.price
        print("  ✅ from_json works")
        
        print("🎉 OrderEvent tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ OrderEvent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enriched_order_event():
    """Test EnrichedOrderEvent model functionality."""
    print("\n🧪 Testing EnrichedOrderEvent model...")
    
    try:
        from com.goodrx.flink.enrichment.model.order_event import OrderEvent
        from com.goodrx.flink.enrichment.model.enriched_order_event import EnrichedOrderEvent
        
        # Test 1: Create base order
        order = OrderEvent(
            order_id='test-order-001',
            customer_id='customer-123',
            product_id='product-456',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        # Test 2: Create enriched event
        enriched = EnrichedOrderEvent.from_order_event(order)
        assert enriched.order_event == order
        assert enriched.product_name is None
        print("  ✅ Basic enriched event creation works")
        
        # Test 3: Product enrichment
        enriched_with_product = enriched.with_product_enrichment(
            product_name='Test Product',
            product_category='Electronics',
            product_price=149.99
        )
        assert enriched_with_product.product_name == 'Test Product'
        assert enriched_with_product.product_category == 'Electronics'
        assert enriched_with_product.product_price == 149.99
        print("  ✅ Product enrichment works")
        
        # Test 4: Customer enrichment
        fully_enriched = enriched_with_product.with_customer_enrichment(
            customer_name='John Doe',
            customer_email='<EMAIL>',
            customer_tier='Gold'
        )
        assert fully_enriched.customer_name == 'John Doe'
        assert fully_enriched.customer_email == '<EMAIL>'
        assert fully_enriched.customer_tier == 'Gold'
        print("  ✅ Customer enrichment works")
        
        # Test 5: Fully enriched check
        assert fully_enriched.is_fully_enriched() is True
        assert enriched.is_fully_enriched() is False
        print("  ✅ Enrichment status check works")
        
        # Test 6: Serialization
        enriched_dict = fully_enriched.to_dict()
        assert enriched_dict['orderId'] == 'test-order-001'
        assert enriched_dict['productName'] == 'Test Product'
        assert enriched_dict['customerName'] == 'John Doe'
        print("  ✅ Enriched event serialization works")
        
        print("🎉 EnrichedOrderEvent tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ EnrichedOrderEvent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_package_imports():
    """Test that model package imports work correctly."""
    print("\n🧪 Testing model package imports...")
    
    try:
        from com.goodrx.flink.enrichment.model import OrderEvent, EnrichedOrderEvent
        
        # Test that we can use the imported classes
        order = OrderEvent('test', 'test', 'test', 1, 10.0, 0)
        enriched = EnrichedOrderEvent.from_order_event(order)
        
        assert order.order_id == 'test'
        assert enriched.order_event == order
        
        print("  ✅ Model package imports work")
        print("🎉 Model package import tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Model package import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance():
    """Test basic performance characteristics."""
    print("\n🧪 Testing performance...")
    
    try:
        from com.goodrx.flink.enrichment.model import OrderEvent, EnrichedOrderEvent
        
        # Test creating many events
        start_time = time.time()
        events = []
        
        for i in range(1000):
            order = OrderEvent(
                order_id=f'order-{i}',
                customer_id=f'customer-{i}',
                product_id=f'product-{i % 10}',
                quantity=i % 5 + 1,
                price=float(i * 10),
                event_timestamp=int(time.time() * 1000)
            )
            enriched = EnrichedOrderEvent.from_order_event(order)
            enriched = enriched.with_product_enrichment(f'Product {i}', 'Category')
            events.append(enriched)
        
        elapsed = time.time() - start_time
        
        assert len(events) == 1000
        assert elapsed < 1.0  # Should create 1000 events in under 1 second
        
        print(f"  ✅ Created 1000 enriched events in {elapsed:.3f}s")
        print(f"  ✅ Rate: {1000/elapsed:.0f} events/second")
        
        print("🎉 Performance tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Running Python Order Enrichment Model Tests")
    print("=" * 50)
    
    tests = [
        test_order_event,
        test_enriched_order_event,
        test_model_package_imports,
        test_performance
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All model tests passed! The Python order enrichment models are working correctly.")
        print("✅ OrderEvent model is functional")
        print("✅ EnrichedOrderEvent model is functional") 
        print("✅ Package imports work correctly")
        print("✅ Performance meets requirements")
        return 0
    else:
        print("❌ Some tests failed. Please check the output above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
