#!/usr/bin/env python3
"""
Unit tests for the order enrichment models.
"""

import sys
import os
import json
import unittest

# Add the source directory to Python path
current_dir = os.path.dirname(__file__)
source_dir = os.path.join(current_dir, '../../../../main/python')
sys.path.insert(0, os.path.abspath(source_dir))

from com.goodrx.flink.enrichment.model import OrderEvent, EnrichedOrderEvent


class TestOrderEvent(unittest.TestCase):
    """Test cases for the OrderEvent model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.valid_order_data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000
        }
    
    def test_order_event_creation(self):
        """Test creating an OrderEvent with all parameters."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        self.assertEqual(order_event.order_id, 'order-123')
        self.assertEqual(order_event.customer_id, 'customer-456')
        self.assertEqual(order_event.product_id, 'product-789')
        self.assertEqual(order_event.quantity, 2)
        self.assertEqual(order_event.price, 99.99)
        self.assertEqual(order_event.event_timestamp, 1640995200000)
    
    def test_from_dict_valid_data(self):
        """Test creating OrderEvent from valid dictionary."""
        order_event = OrderEvent.from_dict(self.valid_order_data)
        
        self.assertEqual(order_event.order_id, 'order-123')
        self.assertEqual(order_event.customer_id, 'customer-456')
        self.assertEqual(order_event.product_id, 'product-789')
        self.assertEqual(order_event.quantity, 2)
        self.assertEqual(order_event.price, 99.99)
        self.assertEqual(order_event.event_timestamp, 1640995200000)
    
    def test_from_dict_missing_timestamp(self):
        """Test creating OrderEvent with missing timestamp (should default to 0)."""
        data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 1,
            'price': 50.0
        }
        
        order_event = OrderEvent.from_dict(data)
        self.assertEqual(order_event.event_timestamp, 0)
    
    def test_from_dict_missing_required_field(self):
        """Test creating OrderEvent with missing required field."""
        data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            # Missing productId
            'quantity': 1,
            'price': 50.0
        }
        
        with self.assertRaises(KeyError):
            OrderEvent.from_dict(data)
    
    def test_to_dict(self):
        """Test converting OrderEvent to dictionary."""
        order_event = OrderEvent.from_dict(self.valid_order_data)
        result = order_event.to_dict()
        self.assertEqual(result, self.valid_order_data)
    
    def test_json_serialization(self):
        """Test JSON serialization and deserialization."""
        order_event = OrderEvent.from_dict(self.valid_order_data)
        
        # Test to_json
        json_string = order_event.to_json()
        parsed = json.loads(json_string)
        self.assertEqual(parsed, self.valid_order_data)
        
        # Test from_json
        order_event2 = OrderEvent.from_json(json_string)
        self.assertEqual(order_event.order_id, order_event2.order_id)
        self.assertEqual(order_event.price, order_event2.price)
    
    def test_string_representations(self):
        """Test string representations of OrderEvent."""
        order_event = OrderEvent.from_dict(self.valid_order_data)
        
        str_repr = str(order_event)
        self.assertIn('order-123', str_repr)
        self.assertIn('customer-456', str_repr)
        
        repr_repr = repr(order_event)
        self.assertIn('OrderEvent', repr_repr)
        self.assertIn('order-123', repr_repr)


class TestEnrichedOrderEvent(unittest.TestCase):
    """Test cases for the EnrichedOrderEvent model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
    
    def test_from_order_event(self):
        """Test creating EnrichedOrderEvent from OrderEvent."""
        enriched_event = EnrichedOrderEvent.from_order_event(self.sample_order_event)
        
        self.assertEqual(enriched_event.order_event, self.sample_order_event)
        self.assertIsNone(enriched_event.product_name)
        self.assertIsNone(enriched_event.customer_name)
    
    def test_with_product_enrichment(self):
        """Test adding product enrichment to an event."""
        base_event = EnrichedOrderEvent.from_order_event(self.sample_order_event)
        
        enriched_event = base_event.with_product_enrichment(
            product_name='Test Product',
            product_category='Electronics',
            product_price=149.99
        )
        
        self.assertEqual(enriched_event.order_event, self.sample_order_event)
        self.assertEqual(enriched_event.product_name, 'Test Product')
        self.assertEqual(enriched_event.product_category, 'Electronics')
        self.assertEqual(enriched_event.product_price, 149.99)
        self.assertIsNone(enriched_event.customer_name)  # Should preserve other fields
    
    def test_with_customer_enrichment(self):
        """Test adding customer enrichment to an event."""
        base_event = EnrichedOrderEvent.from_order_event(self.sample_order_event)
        
        enriched_event = base_event.with_customer_enrichment(
            customer_name='John Doe',
            customer_email='<EMAIL>',
            customer_tier='Gold'
        )
        
        self.assertEqual(enriched_event.order_event, self.sample_order_event)
        self.assertEqual(enriched_event.customer_name, 'John Doe')
        self.assertEqual(enriched_event.customer_email, '<EMAIL>')
        self.assertEqual(enriched_event.customer_tier, 'Gold')
        self.assertIsNone(enriched_event.product_name)  # Should preserve other fields
    
    def test_chained_enrichment(self):
        """Test chaining enrichment operations."""
        enriched_event = (EnrichedOrderEvent.from_order_event(self.sample_order_event)
                         .with_product_enrichment('Test Product', 'Electronics', 149.99)
                         .with_customer_enrichment('John Doe', '<EMAIL>', 'Gold'))
        
        self.assertEqual(enriched_event.product_name, 'Test Product')
        self.assertEqual(enriched_event.product_category, 'Electronics')
        self.assertEqual(enriched_event.product_price, 149.99)
        self.assertEqual(enriched_event.customer_name, 'John Doe')
        self.assertEqual(enriched_event.customer_email, '<EMAIL>')
        self.assertEqual(enriched_event.customer_tier, 'Gold')
    
    def test_is_fully_enriched(self):
        """Test checking if event is fully enriched."""
        # Not enriched
        base_event = EnrichedOrderEvent.from_order_event(self.sample_order_event)
        self.assertFalse(base_event.is_fully_enriched())
        
        # Only product enriched
        product_enriched = base_event.with_product_enrichment('Test Product', 'Electronics')
        self.assertFalse(product_enriched.is_fully_enriched())
        
        # Only customer enriched
        customer_enriched = base_event.with_customer_enrichment('John Doe')
        self.assertFalse(customer_enriched.is_fully_enriched())
        
        # Fully enriched
        fully_enriched = (base_event
                         .with_product_enrichment('Test Product', 'Electronics')
                         .with_customer_enrichment('John Doe'))
        self.assertTrue(fully_enriched.is_fully_enriched())
    
    def test_to_dict_partial_enrichment(self):
        """Test converting partially enriched event to dictionary."""
        enriched_event = EnrichedOrderEvent(
            order_event=self.sample_order_event,
            product_name='Test Product',
            # Other enrichment fields are None
        )
        
        result = enriched_event.to_dict()
        
        # Should only include non-None enrichment fields
        expected = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000,
            'productName': 'Test Product'
        }
        
        self.assertEqual(result, expected)
        self.assertNotIn('productCategory', result)
        self.assertNotIn('customerName', result)
    
    def test_json_serialization(self):
        """Test JSON serialization of enriched events."""
        enriched_event = (EnrichedOrderEvent.from_order_event(self.sample_order_event)
                         .with_product_enrichment('Test Product', 'Electronics')
                         .with_customer_enrichment('John Doe'))
        
        json_string = enriched_event.to_json()
        parsed = json.loads(json_string)
        
        self.assertEqual(parsed['orderId'], 'order-123')
        self.assertEqual(parsed['productName'], 'Test Product')
        self.assertEqual(parsed['customerName'], 'John Doe')
        
        # Test round-trip
        enriched_event2 = EnrichedOrderEvent.from_json(json_string)
        self.assertEqual(enriched_event2.order_event.order_id, enriched_event.order_event.order_id)
        self.assertEqual(enriched_event2.product_name, enriched_event.product_name)
        self.assertEqual(enriched_event2.customer_name, enriched_event.customer_name)


if __name__ == '__main__':
    unittest.main(verbosity=2)
