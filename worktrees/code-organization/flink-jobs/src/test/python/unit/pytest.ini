[tool:pytest]
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
markers =
    integration: marks tests as integration tests (deselect with '-m "not integration"')
    unit: marks tests as unit tests
    slow: marks tests as slow running
    asyncio: marks tests as async tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
