#!/usr/bin/env python3
"""
Comprehensive test runner for Python order enrichment tests.

This script runs both unit tests and integration tests in the proper order
and provides detailed reporting.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
import time


def setup_python_path():
    """Add the source directory to Python path."""
    current_dir = Path(__file__).parent
    source_dir = current_dir / "../../../main/python"
    source_dir_abs = source_dir.resolve()
    
    if str(source_dir_abs) not in sys.path:
        sys.path.insert(0, str(source_dir_abs))
    
    # Also set PYTHONPATH environment variable for subprocess calls
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    if current_pythonpath:
        os.environ['PYTHONPATH'] = f"{source_dir_abs}:{current_pythonpath}"
    else:
        os.environ['PYTHONPATH'] = str(source_dir_abs)


def print_banner(title: str):
    """Print a formatted banner."""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)


def print_section(title: str):
    """Print a section header."""
    print(f"\n📋 {title}")
    print("-" * 40)


def run_unit_tests():
    """Run unit tests."""
    print_section("Running Unit Tests")
    
    test_dir = Path(__file__).parent / "unit"
    
    # Run the simple unit test runner
    print("🔧 Running model and function unit tests...")
    cmd = [sys.executable, "simple_test.py"]
    result = subprocess.run(cmd, cwd=test_dir, capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    if result.returncode == 0:
        print("✅ Unit tests passed!")
        return True
    else:
        print("❌ Unit tests failed!")
        return False


def run_integration_tests():
    """Run integration tests."""
    print_section("Running Integration Tests")
    
    test_dir = Path(__file__).parent / "integration"
    
    # Run the integration test
    print("🔗 Running end-to-end integration tests...")
    cmd = [sys.executable, "integration_test.py"]
    result = subprocess.run(cmd, cwd=test_dir, capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    if result.returncode == 0:
        print("✅ Integration tests passed!")
        return True
    else:
        print("❌ Integration tests failed!")
        return False


def run_pytest_tests():
    """Run pytest-based tests if pytest is available."""
    print_section("Running Pytest Tests (if available)")
    
    try:
        import pytest
        print("🧪 Pytest is available, running pytest-based tests...")
        
        test_dir = Path(__file__).parent / "unit"
        cmd = [
            sys.executable, "-m", "pytest",
            "com/",
            "-v",
            "--tb=short"
        ]
        result = subprocess.run(cmd, cwd=test_dir)
        
        if result.returncode == 0:
            print("✅ Pytest tests passed!")
            return True
        else:
            print("❌ Pytest tests failed!")
            return False
            
    except ImportError:
        print("ℹ️  Pytest not available, skipping pytest-based tests")
        print("   Install with: pip install pytest")
        return True  # Not a failure, just not available


def run_performance_benchmark():
    """Run performance benchmarks."""
    print_section("Running Performance Benchmarks")
    
    try:
        # Setup path
        setup_python_path()
        
        # Import and run performance tests
        from integration.integration_test import test_performance_characteristics
        
        print("⚡ Running performance benchmarks...")
        if test_performance_characteristics():
            print("✅ Performance benchmarks passed!")
            return True
        else:
            print("❌ Performance benchmarks failed!")
            return False
            
    except Exception as e:
        print(f"❌ Performance benchmark failed: {e}")
        return False


def generate_test_report(results: dict):
    """Generate a comprehensive test report."""
    print_banner("Test Report Summary")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📊 Total Test Suites: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 Detailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    if failed_tests == 0:
        print("\n🎉 All tests passed! The Python order enrichment implementation is ready!")
        print("✅ Models are working correctly")
        print("✅ Integration pipeline is functional")
        print("✅ Performance meets requirements")
        print("✅ Ready for PyFlink deployment")
    else:
        print(f"\n⚠️  {failed_tests} test suite(s) failed. Please review the output above.")


def list_available_tests():
    """List all available test files."""
    print_banner("Available Tests")
    
    test_dir = Path(__file__).parent
    
    print("📁 Unit Tests:")
    unit_dir = test_dir / "unit"
    if unit_dir.exists():
        for test_file in unit_dir.rglob("test_*.py"):
            rel_path = test_file.relative_to(unit_dir)
            print(f"  📄 {rel_path}")
        
        # Also list the simple test runner
        simple_test = unit_dir / "simple_test.py"
        if simple_test.exists():
            print(f"  🔧 simple_test.py (unit test runner)")
    
    print("\n📁 Integration Tests:")
    integration_dir = test_dir / "integration"
    if integration_dir.exists():
        for test_file in integration_dir.rglob("*test*.py"):
            rel_path = test_file.relative_to(integration_dir)
            print(f"  🔗 {rel_path}")


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description="Run Python order enrichment tests")
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "pytest", "performance", "all"],
        nargs="?",
        default="all",
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available test files"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run only essential tests (skip performance benchmarks)"
    )
    
    args = parser.parse_args()
    
    # Setup Python path
    setup_python_path()
    
    if args.list:
        list_available_tests()
        return
    
    print_banner("Python Order Enrichment Test Suite")
    print(f"🚀 Running test type: {args.test_type}")
    print(f"📁 Working directory: {Path.cwd()}")
    print(f"🐍 Python version: {sys.version}")
    
    start_time = time.time()
    results = {}
    
    if args.test_type in ["unit", "all"]:
        results["Unit Tests"] = run_unit_tests()
    
    if args.test_type in ["integration", "all"]:
        results["Integration Tests"] = run_integration_tests()
    
    if args.test_type in ["pytest", "all"]:
        results["Pytest Tests"] = run_pytest_tests()
    
    if args.test_type in ["performance", "all"] and not args.quick:
        results["Performance Benchmarks"] = run_performance_benchmark()
    
    # Handle single test type
    if args.test_type == "unit":
        results = {"Unit Tests": results.get("Unit Tests", False)}
    elif args.test_type == "integration":
        results = {"Integration Tests": results.get("Integration Tests", False)}
    elif args.test_type == "pytest":
        results = {"Pytest Tests": results.get("Pytest Tests", False)}
    elif args.test_type == "performance":
        results = {"Performance Benchmarks": results.get("Performance Benchmarks", False)}
    
    elapsed_time = time.time() - start_time
    
    # Generate report
    generate_test_report(results)
    
    print(f"\n⏱️  Total execution time: {elapsed_time:.2f} seconds")
    
    # Exit with appropriate code
    failed_count = sum(1 for result in results.values() if not result)
    sys.exit(failed_count)


if __name__ == "__main__":
    main()
