"""
Unit tests for the OrderEvent model.
"""

import json
import pytest
from typing import Dict, Any

import sys
import os

# Add the source directory to the Python path
current_dir = os.path.dirname(__file__)
source_dir = os.path.join(current_dir, '../../../../../main/python')
sys.path.insert(0, os.path.abspath(source_dir))

from com.goodrx.flink.enrichment.model.order_event import OrderEvent


class TestOrderEvent:
    """Test cases for the OrderEvent model."""
    
    @pytest.fixture
    def valid_order_data(self) -> Dict[str, Any]:
        """Valid order event data for testing."""
        return {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000
        }
    
    @pytest.fixture
    def valid_order_event(self, valid_order_data) -> OrderEvent:
        """Valid OrderEvent instance for testing."""
        return OrderEvent.from_dict(valid_order_data)
    
    def test_order_event_creation(self):
        """Test creating an OrderEvent with all parameters."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert order_event.order_id == 'order-123'
        assert order_event.customer_id == 'customer-456'
        assert order_event.product_id == 'product-789'
        assert order_event.quantity == 2
        assert order_event.price == 99.99
        assert order_event.event_timestamp == 1640995200000
    
    def test_from_dict_valid_data(self, valid_order_data):
        """Test creating OrderEvent from valid dictionary."""
        order_event = OrderEvent.from_dict(valid_order_data)
        
        assert order_event.order_id == 'order-123'
        assert order_event.customer_id == 'customer-456'
        assert order_event.product_id == 'product-789'
        assert order_event.quantity == 2
        assert order_event.price == 99.99
        assert order_event.event_timestamp == 1640995200000
    
    def test_from_dict_missing_timestamp(self):
        """Test creating OrderEvent with missing timestamp (should default to 0)."""
        data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 1,
            'price': 50.0
        }
        
        order_event = OrderEvent.from_dict(data)
        assert order_event.event_timestamp == 0
    
    def test_from_dict_missing_required_field(self):
        """Test creating OrderEvent with missing required field."""
        data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            # Missing productId
            'quantity': 1,
            'price': 50.0
        }
        
        with pytest.raises(KeyError):
            OrderEvent.from_dict(data)
    
    def test_from_dict_invalid_types(self):
        """Test creating OrderEvent with invalid data types."""
        data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 'not_a_number',  # Should be int
            'price': 50.0
        }
        
        with pytest.raises((TypeError, ValueError)):
            OrderEvent.from_dict(data)
    
    def test_from_json_valid(self, valid_order_data):
        """Test creating OrderEvent from valid JSON string."""
        json_string = json.dumps(valid_order_data)
        order_event = OrderEvent.from_json(json_string)
        
        assert order_event.order_id == 'order-123'
        assert order_event.customer_id == 'customer-456'
        assert order_event.product_id == 'product-789'
        assert order_event.quantity == 2
        assert order_event.price == 99.99
        assert order_event.event_timestamp == 1640995200000
    
    def test_from_json_invalid(self):
        """Test creating OrderEvent from invalid JSON string."""
        invalid_json = "not a json string"
        
        with pytest.raises(json.JSONDecodeError):
            OrderEvent.from_json(invalid_json)
    
    def test_to_dict(self, valid_order_event):
        """Test converting OrderEvent to dictionary."""
        result = valid_order_event.to_dict()
        
        expected = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000
        }
        
        assert result == expected
    
    def test_to_json(self, valid_order_event):
        """Test converting OrderEvent to JSON string."""
        json_string = valid_order_event.to_json()
        
        # Parse back to verify
        parsed = json.loads(json_string)
        
        expected = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000
        }
        
        assert parsed == expected
    
    def test_round_trip_serialization(self, valid_order_data):
        """Test round-trip serialization: dict -> OrderEvent -> dict."""
        # Create from dict
        order_event = OrderEvent.from_dict(valid_order_data)
        
        # Convert back to dict
        result_dict = order_event.to_dict()
        
        # Should be identical
        assert result_dict == valid_order_data
    
    def test_round_trip_json_serialization(self, valid_order_data):
        """Test round-trip JSON serialization: JSON -> OrderEvent -> JSON."""
        # Create JSON string
        original_json = json.dumps(valid_order_data)
        
        # Parse to OrderEvent
        order_event = OrderEvent.from_json(original_json)
        
        # Convert back to JSON
        result_json = order_event.to_json()
        
        # Parse both to compare (order might differ)
        original_parsed = json.loads(original_json)
        result_parsed = json.loads(result_json)
        
        assert result_parsed == original_parsed
    
    def test_string_representations(self, valid_order_event):
        """Test string representations of OrderEvent."""
        str_repr = str(valid_order_event)
        repr_repr = repr(valid_order_event)
        
        # Should contain key information
        assert 'order-123' in str_repr
        assert 'customer-456' in str_repr
        assert 'product-789' in str_repr
        
        assert 'order-123' in repr_repr
        assert 'customer-456' in repr_repr
        assert 'product-789' in repr_repr
        assert 'OrderEvent' in repr_repr
    
    def test_type_coercion(self):
        """Test that OrderEvent properly coerces types."""
        data = {
            'orderId': 123,  # Should be converted to string
            'customerId': 456,  # Should be converted to string
            'productId': 789,  # Should be converted to string
            'quantity': '2',  # Should be converted to int
            'price': '99.99',  # Should be converted to float
            'eventTimestamp': '1640995200000'  # Should be converted to int
        }
        
        order_event = OrderEvent.from_dict(data)
        
        assert order_event.order_id == '123'
        assert order_event.customer_id == '456'
        assert order_event.product_id == '789'
        assert order_event.quantity == 2
        assert order_event.price == 99.99
        assert order_event.event_timestamp == 1640995200000
    
    def test_edge_cases(self):
        """Test edge cases for OrderEvent."""
        # Test with zero values
        data = {
            'orderId': 'order-000',
            'customerId': 'customer-000',
            'productId': 'product-000',
            'quantity': 0,
            'price': 0.0,
            'eventTimestamp': 0
        }
        
        order_event = OrderEvent.from_dict(data)
        
        assert order_event.order_id == 'order-000'
        assert order_event.quantity == 0
        assert order_event.price == 0.0
        assert order_event.event_timestamp == 0
    
    def test_large_values(self):
        """Test OrderEvent with large values."""
        data = {
            'orderId': 'order-' + 'x' * 1000,  # Very long ID
            'customerId': 'customer-' + 'y' * 1000,
            'productId': 'product-' + 'z' * 1000,
            'quantity': 999999,  # Large quantity
            'price': 999999.99,  # Large price
            'eventTimestamp': 9999999999999  # Large timestamp
        }
        
        order_event = OrderEvent.from_dict(data)
        
        assert len(order_event.order_id) == 1006  # 'order-' + 1000 chars
        assert order_event.quantity == 999999
        assert order_event.price == 999999.99
        assert order_event.event_timestamp == 9999999999999


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
