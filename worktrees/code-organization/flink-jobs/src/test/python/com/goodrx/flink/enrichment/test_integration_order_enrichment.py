"""
Integration tests for the Python OrderGrpcEnrichmentJob.

These tests simulate the complete end-to-end flow of the enrichment job,
including Kafka sources, processing functions, and sinks.
"""

import asyncio
import json
import logging
import pytest
import time
from typing import List, Dict, Any
from unittest.mock import Mock, AsyncMock, patch
from concurrent.futures import Future

import sys
import os

# Add the source directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../main/python'))

from com.goodrx.flink.enrichment.model import OrderEvent, EnrichedOrderEvent
from com.goodrx.flink.enrichment.functions import (
    JsonToOrderEventParser, 
    ProductEnrichmentFunction,
    MockProductService,
    OrderEventValidator
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestOrderEnrichmentIntegration:
    """Integration tests for the complete order enrichment pipeline."""
    
    @pytest.fixture
    def sample_order_events(self) -> List[Dict[str, Any]]:
        """Sample order events for testing."""
        return [
            {
                'orderId': 'order-001',
                'customerId': 'customer-001',
                'productId': 'product-001',
                'quantity': 2,
                'price': 99.99,
                'eventTimestamp': int(time.time() * 1000)
            },
            {
                'orderId': 'order-002',
                'customerId': 'customer-002',
                'productId': 'product-002',
                'quantity': 1,
                'price': 15.99,
                'eventTimestamp': int(time.time() * 1000)
            },
            {
                'orderId': 'order-003',
                'customerId': 'customer-003',
                'productId': 'product-003',
                'quantity': 1,
                'price': 129.99,
                'eventTimestamp': int(time.time() * 1000)
            }
        ]
    
    @pytest.fixture
    def malformed_records(self) -> List[str]:
        """Sample malformed records for testing error handling."""
        return [
            '{"invalid": "json", "missing": "required_fields"}',
            'not a json string at all',
            '{"orderId": "order-004", "missing": "other_required_fields"}',
            '{"orderId": "", "customerId": "", "productId": ""}',  # Empty fields
        ]
    
    @pytest.fixture
    def mock_product_service(self) -> MockProductService:
        """Mock product service with test data."""
        service = MockProductService()
        # Add some additional test products
        service.add_product('product-test-001', 'Test Product 1', 'Test Category', 50.0)
        service.add_product('product-test-002', 'Test Product 2', 'Test Category', 75.0)
        return service
    
    def test_json_parsing_pipeline(self, sample_order_events, malformed_records):
        """Test the JSON parsing pipeline with both valid and invalid records."""
        parser = JsonToOrderEventParser("malformed-records")
        
        # Mock context for side output
        mock_context = Mock()
        mock_context.output = Mock()
        
        valid_results = []
        malformed_count = 0
        
        # Process valid records
        for event_data in sample_order_events:
            json_string = json.dumps(event_data)
            results = list(parser.process_element(json_string, mock_context))
            valid_results.extend(results)
        
        # Process malformed records
        for malformed_record in malformed_records:
            results = list(parser.process_element(malformed_record, mock_context))
            if not results:  # No results means it was sent to side output
                malformed_count += 1
        
        # Assertions
        assert len(valid_results) == len(sample_order_events)
        assert malformed_count == len(malformed_records)
        assert mock_context.output.call_count == len(malformed_records)
        
        # Verify parsed events
        for i, order_event in enumerate(valid_results):
            assert isinstance(order_event, OrderEvent)
            assert order_event.order_id == sample_order_events[i]['orderId']
            assert order_event.customer_id == sample_order_events[i]['customerId']
            assert order_event.product_id == sample_order_events[i]['productId']
    
    @pytest.mark.asyncio
    async def test_product_enrichment_pipeline(self, sample_order_events, mock_product_service):
        """Test the product enrichment pipeline with async operations."""
        # Create order events
        order_events = [OrderEvent.from_dict(data) for data in sample_order_events]
        
        # Create enrichment function
        enrichment_function = ProductEnrichmentFunction(
            product_service_host="localhost",
            product_service_port=50051,
            use_tls=False
        )
        
        # Mock runtime context
        mock_runtime_context = Mock()
        enrichment_function.open(mock_runtime_context)
        
        # Process each order event
        enriched_results = []
        for order_event in order_events:
            # Create a future for the result
            result_future = Future()
            
            # Process the event asynchronously
            await enrichment_function.async_invoke(order_event, result_future)
            
            # Get the result
            enriched_events = result_future.result()
            enriched_results.extend(enriched_events)
        
        # Assertions
        assert len(enriched_results) == len(order_events)
        
        for enriched_event in enriched_results:
            assert isinstance(enriched_event, EnrichedOrderEvent)
            assert enriched_event.order_event is not None
            # Product enrichment should have occurred (even if mock data)
            assert enriched_event.product_name is not None
            assert enriched_event.enrichment_timestamp is not None
        
        # Cleanup
        enrichment_function.close()
    
    @pytest.mark.asyncio
    async def test_end_to_end_enrichment_flow(self, sample_order_events):
        """Test the complete end-to-end enrichment flow."""
        # Step 1: JSON Parsing
        parser = JsonToOrderEventParser("malformed-records")
        mock_context = Mock()
        mock_context.output = Mock()
        
        parsed_events = []
        for event_data in sample_order_events:
            json_string = json.dumps(event_data)
            results = list(parser.process_element(json_string, mock_context))
            parsed_events.extend(results)
        
        # Step 2: Product Enrichment
        enrichment_function = ProductEnrichmentFunction(
            product_service_host="localhost",
            product_service_port=50051,
            use_tls=False
        )
        
        mock_runtime_context = Mock()
        enrichment_function.open(mock_runtime_context)
        
        final_results = []
        for order_event in parsed_events:
            result_future = Future()
            await enrichment_function.async_invoke(order_event, result_future)
            enriched_events = result_future.result()
            final_results.extend(enriched_events)
        
        # Step 3: Validation
        assert len(final_results) == len(sample_order_events)
        
        for i, enriched_event in enumerate(final_results):
            # Verify the enriched event structure
            assert isinstance(enriched_event, EnrichedOrderEvent)
            assert enriched_event.order_event.order_id == sample_order_events[i]['orderId']
            
            # Verify enrichment occurred
            assert enriched_event.product_name is not None
            assert enriched_event.product_category is not None
            assert enriched_event.enrichment_timestamp is not None
            
            # Verify serialization works
            event_dict = enriched_event.to_dict()
            assert 'orderId' in event_dict
            assert 'productName' in event_dict
            assert 'productCategory' in event_dict
            assert 'enrichmentTimestamp' in event_dict
            
            # Verify JSON serialization works
            json_string = enriched_event.to_json()
            assert isinstance(json_string, str)
            
            # Verify we can deserialize back
            deserialized = EnrichedOrderEvent.from_json(json_string)
            assert deserialized.order_event.order_id == enriched_event.order_event.order_id
        
        # Cleanup
        enrichment_function.close()
    
    def test_error_handling_and_resilience(self):
        """Test error handling and resilience of the enrichment pipeline."""
        # Test with various error conditions
        error_cases = [
            '{"orderId": null, "customerId": "customer-001", "productId": "product-001"}',
            '{"orderId": "order-001", "customerId": "customer-001", "quantity": "invalid"}',
            '{"orderId": "order-001", "customerId": "customer-001", "price": "not_a_number"}',
        ]
        
        parser = JsonToOrderEventParser("malformed-records")
        mock_context = Mock()
        mock_context.output = Mock()
        
        error_count = 0
        for error_case in error_cases:
            try:
                results = list(parser.process_element(error_case, mock_context))
                if not results:  # Sent to side output
                    error_count += 1
            except Exception:
                error_count += 1
        
        # All error cases should be handled gracefully
        assert error_count == len(error_cases)
    
    def test_order_event_validation(self, sample_order_events):
        """Test order event validation logic."""
        validator = OrderEventValidator()
        
        # Test valid events
        for event_data in sample_order_events:
            order_event = OrderEvent.from_dict(event_data)
            assert validator.validate_order_event(order_event) is True
        
        # Test invalid events
        invalid_cases = [
            OrderEvent("", "customer-001", "product-001", 1, 10.0, 0),  # Empty order ID
            OrderEvent("order-001", "", "product-001", 1, 10.0, 0),     # Empty customer ID
            OrderEvent("order-001", "customer-001", "", 1, 10.0, 0),    # Empty product ID
            OrderEvent("order-001", "customer-001", "product-001", 0, 10.0, 0),    # Zero quantity
            OrderEvent("order-001", "customer-001", "product-001", 1, -10.0, 0),   # Negative price
            OrderEvent("order-001", "customer-001", "product-001", 1, 10.0, -1),   # Negative timestamp
        ]
        
        for invalid_event in invalid_cases:
            assert validator.validate_order_event(invalid_event) is False
    
    def test_order_event_sanitization(self):
        """Test order event sanitization logic."""
        validator = OrderEventValidator()
        
        # Test sanitization
        dirty_event = OrderEvent(
            order_id="  order-001  ",  # Extra whitespace
            customer_id="  customer-001  ",
            product_id="  product-001  ",
            quantity=-5,  # Negative quantity
            price=-10.0,  # Negative price
            event_timestamp=12345
        )
        
        sanitized = validator.sanitize_order_event(dirty_event)
        
        assert sanitized.order_id == "order-001"
        assert sanitized.customer_id == "customer-001"
        assert sanitized.product_id == "product-001"
        assert sanitized.quantity == 1  # Should be at least 1
        assert sanitized.price == 0.0   # Should be non-negative
        assert sanitized.event_timestamp == 12345  # Should remain unchanged
    
    @pytest.mark.asyncio
    async def test_performance_and_throughput(self, sample_order_events):
        """Test performance characteristics of the enrichment pipeline."""
        # Create a larger dataset for performance testing
        large_dataset = sample_order_events * 100  # 300 events
        
        start_time = time.time()
        
        # Process all events
        parser = JsonToOrderEventParser("malformed-records")
        mock_context = Mock()
        mock_context.output = Mock()
        
        parsed_events = []
        for event_data in large_dataset:
            json_string = json.dumps(event_data)
            results = list(parser.process_element(json_string, mock_context))
            parsed_events.extend(results)
        
        parsing_time = time.time() - start_time
        
        # Test enrichment performance
        enrichment_start = time.time()
        
        enrichment_function = ProductEnrichmentFunction(
            product_service_host="localhost",
            product_service_port=50051,
            use_tls=False
        )
        
        mock_runtime_context = Mock()
        enrichment_function.open(mock_runtime_context)
        
        enriched_results = []
        for order_event in parsed_events:
            result_future = Future()
            await enrichment_function.async_invoke(order_event, result_future)
            enriched_events = result_future.result()
            enriched_results.extend(enriched_events)
        
        enrichment_time = time.time() - enrichment_start
        total_time = time.time() - start_time
        
        # Performance assertions
        assert len(enriched_results) == len(large_dataset)
        assert parsing_time < 5.0  # Should parse 300 events in under 5 seconds
        assert enrichment_time < 10.0  # Should enrich 300 events in under 10 seconds
        assert total_time < 15.0  # Total processing should be under 15 seconds
        
        # Log performance metrics
        logger.info(f"Performance metrics for {len(large_dataset)} events:")
        logger.info(f"  Parsing time: {parsing_time:.3f}s")
        logger.info(f"  Enrichment time: {enrichment_time:.3f}s")
        logger.info(f"  Total time: {total_time:.3f}s")
        logger.info(f"  Throughput: {len(large_dataset)/total_time:.1f} events/second")
        
        enrichment_function.close()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
