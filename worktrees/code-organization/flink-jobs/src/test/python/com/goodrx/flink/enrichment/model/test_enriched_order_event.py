"""
Unit tests for the EnrichedOrderEvent model.
"""

import json
import pytest
from typing import Dict, Any

import sys
import os

# Add the source directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../../main/python'))

from com.goodrx.flink.enrichment.model.order_event import OrderEvent
from com.goodrx.flink.enrichment.model.enriched_order_event import EnrichedOrderEvent


class TestEnrichedOrderEvent:
    """Test cases for the EnrichedOrderEvent model."""
    
    @pytest.fixture
    def sample_order_event(self) -> OrderEvent:
        """Sample OrderEvent for testing."""
        return OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
    
    @pytest.fixture
    def enriched_event_data(self) -> Dict[str, Any]:
        """Sample enriched event data."""
        return {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000,
            'productName': 'Test Product',
            'productCategory': 'Electronics',
            'productPrice': 149.99,
            'customerName': 'John Doe',
            'customerEmail': '<EMAIL>',
            'customerTier': 'Gold',
            'enrichmentTimestamp': 1640995260000
        }
    
    def test_enriched_order_event_creation(self, sample_order_event):
        """Test creating an EnrichedOrderEvent."""
        enriched_event = EnrichedOrderEvent(
            order_event=sample_order_event,
            product_name='Test Product',
            product_category='Electronics',
            customer_name='John Doe'
        )
        
        assert enriched_event.order_event == sample_order_event
        assert enriched_event.product_name == 'Test Product'
        assert enriched_event.product_category == 'Electronics'
        assert enriched_event.customer_name == 'John Doe'
        assert enriched_event.customer_email is None
        assert enriched_event.customer_tier is None
    
    def test_from_order_event(self, sample_order_event):
        """Test creating EnrichedOrderEvent from OrderEvent."""
        enriched_event = EnrichedOrderEvent.from_order_event(sample_order_event)
        
        assert enriched_event.order_event == sample_order_event
        assert enriched_event.product_name is None
        assert enriched_event.product_category is None
        assert enriched_event.customer_name is None
    
    def test_from_dict(self, enriched_event_data):
        """Test creating EnrichedOrderEvent from dictionary."""
        enriched_event = EnrichedOrderEvent.from_dict(enriched_event_data)
        
        assert enriched_event.order_event.order_id == 'order-123'
        assert enriched_event.order_event.customer_id == 'customer-456'
        assert enriched_event.order_event.product_id == 'product-789'
        assert enriched_event.product_name == 'Test Product'
        assert enriched_event.product_category == 'Electronics'
        assert enriched_event.product_price == 149.99
        assert enriched_event.customer_name == 'John Doe'
        assert enriched_event.customer_email == '<EMAIL>'
        assert enriched_event.customer_tier == 'Gold'
        assert enriched_event.enrichment_timestamp == 1640995260000
    
    def test_from_json(self, enriched_event_data):
        """Test creating EnrichedOrderEvent from JSON string."""
        json_string = json.dumps(enriched_event_data)
        enriched_event = EnrichedOrderEvent.from_json(json_string)
        
        assert enriched_event.order_event.order_id == 'order-123'
        assert enriched_event.product_name == 'Test Product'
        assert enriched_event.customer_name == 'John Doe'
    
    def test_to_dict_full_enrichment(self, sample_order_event):
        """Test converting fully enriched event to dictionary."""
        enriched_event = EnrichedOrderEvent(
            order_event=sample_order_event,
            product_name='Test Product',
            product_category='Electronics',
            product_price=149.99,
            customer_name='John Doe',
            customer_email='<EMAIL>',
            customer_tier='Gold',
            enrichment_timestamp=1640995260000
        )
        
        result = enriched_event.to_dict()
        
        expected = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000,
            'productName': 'Test Product',
            'productCategory': 'Electronics',
            'productPrice': 149.99,
            'customerName': 'John Doe',
            'customerEmail': '<EMAIL>',
            'customerTier': 'Gold',
            'enrichmentTimestamp': 1640995260000
        }
        
        assert result == expected
    
    def test_to_dict_partial_enrichment(self, sample_order_event):
        """Test converting partially enriched event to dictionary."""
        enriched_event = EnrichedOrderEvent(
            order_event=sample_order_event,
            product_name='Test Product',
            # Other enrichment fields are None
        )
        
        result = enriched_event.to_dict()
        
        # Should only include non-None enrichment fields
        expected = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000,
            'productName': 'Test Product'
        }
        
        assert result == expected
        assert 'productCategory' not in result
        assert 'customerName' not in result
    
    def test_to_json(self, sample_order_event):
        """Test converting EnrichedOrderEvent to JSON string."""
        enriched_event = EnrichedOrderEvent(
            order_event=sample_order_event,
            product_name='Test Product',
            customer_name='John Doe'
        )
        
        json_string = enriched_event.to_json()
        
        # Parse back to verify
        parsed = json.loads(json_string)
        
        assert parsed['orderId'] == 'order-123'
        assert parsed['productName'] == 'Test Product'
        assert parsed['customerName'] == 'John Doe'
        assert 'productCategory' not in parsed  # Should not include None values
    
    def test_with_product_enrichment(self, sample_order_event):
        """Test adding product enrichment to an event."""
        base_event = EnrichedOrderEvent.from_order_event(sample_order_event)
        
        enriched_event = base_event.with_product_enrichment(
            product_name='Test Product',
            product_category='Electronics',
            product_price=149.99
        )
        
        assert enriched_event.order_event == sample_order_event
        assert enriched_event.product_name == 'Test Product'
        assert enriched_event.product_category == 'Electronics'
        assert enriched_event.product_price == 149.99
        assert enriched_event.customer_name is None  # Should preserve other fields
    
    def test_with_customer_enrichment(self, sample_order_event):
        """Test adding customer enrichment to an event."""
        base_event = EnrichedOrderEvent.from_order_event(sample_order_event)
        
        enriched_event = base_event.with_customer_enrichment(
            customer_name='John Doe',
            customer_email='<EMAIL>',
            customer_tier='Gold'
        )
        
        assert enriched_event.order_event == sample_order_event
        assert enriched_event.customer_name == 'John Doe'
        assert enriched_event.customer_email == '<EMAIL>'
        assert enriched_event.customer_tier == 'Gold'
        assert enriched_event.product_name is None  # Should preserve other fields
    
    def test_chained_enrichment(self, sample_order_event):
        """Test chaining enrichment operations."""
        enriched_event = (EnrichedOrderEvent.from_order_event(sample_order_event)
                         .with_product_enrichment('Test Product', 'Electronics', 149.99)
                         .with_customer_enrichment('John Doe', '<EMAIL>', 'Gold'))
        
        assert enriched_event.product_name == 'Test Product'
        assert enriched_event.product_category == 'Electronics'
        assert enriched_event.product_price == 149.99
        assert enriched_event.customer_name == 'John Doe'
        assert enriched_event.customer_email == '<EMAIL>'
        assert enriched_event.customer_tier == 'Gold'
    
    def test_is_fully_enriched(self, sample_order_event):
        """Test checking if event is fully enriched."""
        # Not enriched
        base_event = EnrichedOrderEvent.from_order_event(sample_order_event)
        assert base_event.is_fully_enriched() is False
        
        # Only product enriched
        product_enriched = base_event.with_product_enrichment('Test Product', 'Electronics')
        assert product_enriched.is_fully_enriched() is False
        
        # Only customer enriched
        customer_enriched = base_event.with_customer_enrichment('John Doe')
        assert customer_enriched.is_fully_enriched() is False
        
        # Fully enriched
        fully_enriched = (base_event
                         .with_product_enrichment('Test Product', 'Electronics')
                         .with_customer_enrichment('John Doe'))
        assert fully_enriched.is_fully_enriched() is True
    
    def test_round_trip_serialization(self, enriched_event_data):
        """Test round-trip serialization: dict -> EnrichedOrderEvent -> dict."""
        # Create from dict
        enriched_event = EnrichedOrderEvent.from_dict(enriched_event_data)
        
        # Convert back to dict
        result_dict = enriched_event.to_dict()
        
        # Should be identical
        assert result_dict == enriched_event_data
    
    def test_round_trip_json_serialization(self, enriched_event_data):
        """Test round-trip JSON serialization."""
        # Create JSON string
        original_json = json.dumps(enriched_event_data)
        
        # Parse to EnrichedOrderEvent
        enriched_event = EnrichedOrderEvent.from_json(original_json)
        
        # Convert back to JSON
        result_json = enriched_event.to_json()
        
        # Parse both to compare
        original_parsed = json.loads(original_json)
        result_parsed = json.loads(result_json)
        
        assert result_parsed == original_parsed
    
    def test_string_representations(self, sample_order_event):
        """Test string representations of EnrichedOrderEvent."""
        enriched_event = EnrichedOrderEvent(
            order_event=sample_order_event,
            product_name='Test Product',
            customer_name='John Doe'
        )
        
        str_repr = str(enriched_event)
        repr_repr = repr(enriched_event)
        
        # Should contain key information
        assert 'order-123' in str_repr
        assert 'Test Product' in str_repr
        assert 'John Doe' in str_repr
        
        assert 'EnrichedOrderEvent' in repr_repr
        assert 'order-123' in repr_repr
    
    def test_immutability_of_enrichment_methods(self, sample_order_event):
        """Test that enrichment methods return new instances."""
        base_event = EnrichedOrderEvent.from_order_event(sample_order_event)
        
        product_enriched = base_event.with_product_enrichment('Test Product', 'Electronics')
        customer_enriched = base_event.with_customer_enrichment('John Doe')
        
        # Original should be unchanged
        assert base_event.product_name is None
        assert base_event.customer_name is None
        
        # New instances should have enrichment
        assert product_enriched.product_name == 'Test Product'
        assert customer_enriched.customer_name == 'John Doe'
        
        # They should be different instances
        assert base_event is not product_enriched
        assert base_event is not customer_enriched
        assert product_enriched is not customer_enriched


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
