"""
Unit tests for the JSON parser functions.
"""

import json
import pytest
from unittest.mock import Mock

import sys
import os

# Add the source directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../../main/python'))

from com.goodrx.flink.enrichment.model import OrderEvent
from com.goodrx.flink.enrichment.functions.json_parser import JsonTo<PERSON>rderEventParser, OrderEventValidator


class TestJsonToOrderEventParser:
    """Test cases for the JsonToOrderEventParser function."""
    
    @pytest.fixture
    def parser(self):
        """Create a parser instance for testing."""
        return JsonToOrderEventParser("malformed-tag")
    
    @pytest.fixture
    def mock_context(self):
        """Create a mock context for testing."""
        context = Mock()
        context.output = Mock()
        return context
    
    @pytest.fixture
    def valid_order_data(self):
        """Valid order event data."""
        return {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 2,
            'price': 99.99,
            'eventTimestamp': 1640995200000
        }
    
    def test_parse_valid_json(self, parser, mock_context, valid_order_data):
        """Test parsing valid JSON string."""
        json_string = json.dumps(valid_order_data)
        
        # Process the element
        results = list(parser.process_element(json_string, mock_context))
        
        assert len(results) == 1
        order_event = results[0]
        assert isinstance(order_event, OrderEvent)
        assert order_event.order_id == 'order-123'
        assert order_event.customer_id == 'customer-456'
        assert order_event.product_id == 'product-789'
        assert order_event.quantity == 2
        assert order_event.price == 99.99
        
        # Should not call side output for valid data
        mock_context.output.assert_not_called()
    
    def test_parse_invalid_json(self, parser, mock_context):
        """Test parsing invalid JSON string."""
        invalid_json = "not a json string"
        
        # Process the element
        results = list(parser.process_element(invalid_json, mock_context))
        
        # Should yield no results
        assert len(results) == 0
        
        # Should send to side output
        mock_context.output.assert_called_once_with("malformed-tag", invalid_json)
    
    def test_parse_json_missing_required_fields(self, parser, mock_context):
        """Test parsing JSON with missing required fields."""
        incomplete_data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            # Missing productId, quantity, price
        }
        json_string = json.dumps(incomplete_data)
        
        # Process the element
        results = list(parser.process_element(json_string, mock_context))
        
        # Should yield no results
        assert len(results) == 0
        
        # Should send to side output
        mock_context.output.assert_called_once_with("malformed-tag", json_string)
    
    def test_parse_json_invalid_types(self, parser, mock_context):
        """Test parsing JSON with invalid data types."""
        invalid_data = {
            'orderId': 'order-123',
            'customerId': 'customer-456',
            'productId': 'product-789',
            'quantity': 'not_a_number',  # Should be int
            'price': 99.99
        }
        json_string = json.dumps(invalid_data)
        
        # Process the element
        results = list(parser.process_element(json_string, mock_context))
        
        # Should yield no results
        assert len(results) == 0
        
        # Should send to side output
        mock_context.output.assert_called_once_with("malformed-tag", json_string)
    
    def test_parse_multiple_valid_records(self, parser, mock_context):
        """Test parsing multiple valid records."""
        records = [
            {'orderId': 'order-001', 'customerId': 'customer-001', 'productId': 'product-001', 'quantity': 1, 'price': 10.0},
            {'orderId': 'order-002', 'customerId': 'customer-002', 'productId': 'product-002', 'quantity': 2, 'price': 20.0},
            {'orderId': 'order-003', 'customerId': 'customer-003', 'productId': 'product-003', 'quantity': 3, 'price': 30.0},
        ]
        
        all_results = []
        for record in records:
            json_string = json.dumps(record)
            results = list(parser.process_element(json_string, mock_context))
            all_results.extend(results)
        
        assert len(all_results) == 3
        
        for i, order_event in enumerate(all_results):
            assert isinstance(order_event, OrderEvent)
            assert order_event.order_id == f'order-{i+1:03d}'
            assert order_event.quantity == i + 1
            assert order_event.price == (i + 1) * 10.0
        
        # Should not call side output for valid data
        mock_context.output.assert_not_called()
    
    def test_parse_mixed_valid_invalid_records(self, parser, mock_context):
        """Test parsing a mix of valid and invalid records."""
        records = [
            '{"orderId": "order-001", "customerId": "customer-001", "productId": "product-001", "quantity": 1, "price": 10.0}',
            'invalid json string',
            '{"orderId": "order-002", "customerId": "customer-002", "productId": "product-002", "quantity": 2, "price": 20.0}',
            '{"orderId": "order-003", "missing": "required_fields"}',
            '{"orderId": "order-004", "customerId": "customer-004", "productId": "product-004", "quantity": 4, "price": 40.0}',
        ]
        
        all_results = []
        for record in records:
            results = list(parser.process_element(record, mock_context))
            all_results.extend(results)
        
        # Should have 3 valid results
        assert len(all_results) == 3
        
        # Should have called side output 2 times for invalid records
        assert mock_context.output.call_count == 2
    
    def test_parser_statistics_tracking(self, parser, mock_context, valid_order_data):
        """Test that parser tracks statistics correctly."""
        # Initialize parser
        parser.open(None)
        
        # Process valid records
        for i in range(5):
            json_string = json.dumps(valid_order_data)
            list(parser.process_element(json_string, mock_context))
        
        # Process invalid records
        for i in range(3):
            list(parser.process_element("invalid json", mock_context))
        
        assert parser.successful_parses == 5
        assert parser.failed_parses == 3
        
        # Close parser
        parser.close()


class TestOrderEventValidator:
    """Test cases for the OrderEventValidator utility class."""
    
    def test_validate_valid_order_event(self):
        """Test validating a valid OrderEvent."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is True
    
    def test_validate_empty_order_id(self):
        """Test validating OrderEvent with empty order ID."""
        order_event = OrderEvent(
            order_id='',  # Empty
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_validate_empty_customer_id(self):
        """Test validating OrderEvent with empty customer ID."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='',  # Empty
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_validate_empty_product_id(self):
        """Test validating OrderEvent with empty product ID."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='',  # Empty
            quantity=2,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_validate_zero_quantity(self):
        """Test validating OrderEvent with zero quantity."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=0,  # Zero
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_validate_negative_quantity(self):
        """Test validating OrderEvent with negative quantity."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=-1,  # Negative
            price=99.99,
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_validate_negative_price(self):
        """Test validating OrderEvent with negative price."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=-10.0,  # Negative
            event_timestamp=1640995200000
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_validate_negative_timestamp(self):
        """Test validating OrderEvent with negative timestamp."""
        order_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=2,
            price=99.99,
            event_timestamp=-1  # Negative
        )
        
        assert OrderEventValidator.validate_order_event(order_event) is False
    
    def test_sanitize_order_event(self):
        """Test sanitizing an OrderEvent."""
        dirty_event = OrderEvent(
            order_id='  order-123  ',  # Extra whitespace
            customer_id='  customer-456  ',
            product_id='  product-789  ',
            quantity=-5,  # Negative quantity
            price=-10.0,  # Negative price
            event_timestamp=1640995200000
        )
        
        sanitized = OrderEventValidator.sanitize_order_event(dirty_event)
        
        assert sanitized.order_id == 'order-123'
        assert sanitized.customer_id == 'customer-456'
        assert sanitized.product_id == 'product-789'
        assert sanitized.quantity == 1  # Should be at least 1
        assert sanitized.price == 0.0   # Should be non-negative
        assert sanitized.event_timestamp == 1640995200000  # Should remain unchanged
    
    def test_sanitize_preserves_valid_values(self):
        """Test that sanitization preserves valid values."""
        valid_event = OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-789',
            quantity=5,
            price=99.99,
            event_timestamp=1640995200000
        )
        
        sanitized = OrderEventValidator.sanitize_order_event(valid_event)
        
        assert sanitized.order_id == valid_event.order_id
        assert sanitized.customer_id == valid_event.customer_id
        assert sanitized.product_id == valid_event.product_id
        assert sanitized.quantity == valid_event.quantity
        assert sanitized.price == valid_event.price
        assert sanitized.event_timestamp == valid_event.event_timestamp


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
