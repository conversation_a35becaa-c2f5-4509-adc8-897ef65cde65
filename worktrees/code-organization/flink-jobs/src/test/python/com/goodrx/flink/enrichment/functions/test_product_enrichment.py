"""
Unit tests for the product enrichment functions.
"""

import asyncio
import pytest
import time
from concurrent.futures import Future
from unittest.mock import Mock, AsyncMock

import sys
import os

# Add the source directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../../main/python'))

from com.goodrx.flink.enrichment.model import OrderEvent, EnrichedOrderEvent
from com.goodrx.flink.enrichment.functions.product_enrichment import (
    ProductEnrichmentFunction, 
    MockProductService
)


class TestProductEnrichmentFunction:
    """Test cases for the ProductEnrichmentFunction."""
    
    @pytest.fixture
    def sample_order_event(self):
        """Sample OrderEvent for testing."""
        return OrderEvent(
            order_id='order-123',
            customer_id='customer-456',
            product_id='product-001',  # This exists in mock database
            quantity=2,
            price=99.99,
            event_timestamp=int(time.time() * 1000)
        )
    
    @pytest.fixture
    def enrichment_function(self):
        """Create a ProductEnrichmentFunction for testing."""
        return ProductEnrichmentFunction(
            product_service_host="localhost",
            product_service_port=50051,
            use_tls=False
        )
    
    @pytest.fixture
    def mock_runtime_context(self):
        """Create a mock runtime context."""
        return Mock()
    
    def test_function_initialization(self, enrichment_function, mock_runtime_context):
        """Test function initialization and cleanup."""
        # Test open
        enrichment_function.open(mock_runtime_context)
        
        assert enrichment_function.executor is not None
        assert enrichment_function.enrichment_count == 0
        assert enrichment_function.error_count == 0
        
        # Test close
        enrichment_function.close()
        
        # Executor should be shut down
        assert enrichment_function.executor._shutdown is True
    
    @pytest.mark.asyncio
    async def test_successful_enrichment(self, enrichment_function, mock_runtime_context, sample_order_event):
        """Test successful product enrichment."""
        enrichment_function.open(mock_runtime_context)
        
        try:
            # Create a future for the result
            result_future = Future()
            
            # Process the event
            await enrichment_function.async_invoke(sample_order_event, result_future)
            
            # Get the result
            enriched_events = result_future.result()
            
            assert len(enriched_events) == 1
            enriched_event = enriched_events[0]
            
            assert isinstance(enriched_event, EnrichedOrderEvent)
            assert enriched_event.order_event == sample_order_event
            assert enriched_event.product_name == 'Wireless Headphones'  # From mock database
            assert enriched_event.product_category == 'Electronics'
            assert enriched_event.product_price == 99.99
            assert enriched_event.enrichment_timestamp is not None
            
            # Check statistics
            assert enrichment_function.enrichment_count == 1
            assert enrichment_function.error_count == 0
            
        finally:
            enrichment_function.close()
    
    @pytest.mark.asyncio
    async def test_product_not_found(self, enrichment_function, mock_runtime_context):
        """Test enrichment when product is not found."""
        enrichment_function.open(mock_runtime_context)
        
        try:
            # Create order event with non-existent product
            order_event = OrderEvent(
                order_id='order-123',
                customer_id='customer-456',
                product_id='non-existent-product',
                quantity=1,
                price=50.0,
                event_timestamp=int(time.time() * 1000)
            )
            
            result_future = Future()
            await enrichment_function.async_invoke(order_event, result_future)
            
            enriched_events = result_future.result()
            
            assert len(enriched_events) == 1
            enriched_event = enriched_events[0]
            
            # Should return event without enrichment
            assert enriched_event.order_event == order_event
            assert enriched_event.product_name is None
            assert enriched_event.product_category is None
            
        finally:
            enrichment_function.close()
    
    @pytest.mark.asyncio
    async def test_enrichment_timeout_handling(self, enrichment_function, mock_runtime_context, sample_order_event):
        """Test timeout handling in enrichment."""
        enrichment_function.open(mock_runtime_context)
        
        try:
            result_future = Future()
            
            # Simulate timeout
            enrichment_function.timeout(sample_order_event, result_future)
            
            enriched_events = result_future.result()
            
            assert len(enriched_events) == 1
            enriched_event = enriched_events[0]
            
            # Should return event without enrichment
            assert enriched_event.order_event == sample_order_event
            assert enriched_event.product_name is None
            
            # Error count should be incremented
            assert enrichment_function.error_count == 1
            
        finally:
            enrichment_function.close()
    
    @pytest.mark.asyncio
    async def test_multiple_enrichments(self, enrichment_function, mock_runtime_context):
        """Test multiple enrichments to verify statistics tracking."""
        enrichment_function.open(mock_runtime_context)
        
        try:
            order_events = [
                OrderEvent(f'order-{i}', f'customer-{i}', 'product-001', 1, 10.0, int(time.time() * 1000))
                for i in range(5)
            ]
            
            for order_event in order_events:
                result_future = Future()
                await enrichment_function.async_invoke(order_event, result_future)
                enriched_events = result_future.result()
                
                assert len(enriched_events) == 1
                assert enriched_events[0].product_name == 'Wireless Headphones'
            
            # Check statistics
            assert enrichment_function.enrichment_count == 5
            assert enrichment_function.error_count == 0
            
        finally:
            enrichment_function.close()
    
    @pytest.mark.asyncio
    async def test_fetch_product_data_method(self, enrichment_function):
        """Test the internal _fetch_product_data method."""
        # Test existing product
        product_data = await enrichment_function._fetch_product_data('product-001')
        
        assert product_data is not None
        assert product_data['name'] == 'Wireless Headphones'
        assert product_data['category'] == 'Electronics'
        assert product_data['price'] == 99.99
        
        # Test non-existent product
        product_data = await enrichment_function._fetch_product_data('non-existent')
        assert product_data is None


class TestMockProductService:
    """Test cases for the MockProductService."""
    
    @pytest.fixture
    def mock_service(self):
        """Create a MockProductService for testing."""
        return MockProductService()
    
    @pytest.mark.asyncio
    async def test_get_existing_product(self, mock_service):
        """Test getting an existing product."""
        product_data = await mock_service.get_product('product-001')
        
        assert product_data is not None
        assert product_data['name'] == 'Wireless Headphones'
        assert product_data['category'] == 'Electronics'
        assert product_data['price'] == 99.99
    
    @pytest.mark.asyncio
    async def test_get_non_existent_product(self, mock_service):
        """Test getting a non-existent product."""
        product_data = await mock_service.get_product('non-existent')
        assert product_data is None
    
    @pytest.mark.asyncio
    async def test_get_all_default_products(self, mock_service):
        """Test that all default products are available."""
        expected_products = [
            'product-001', 'product-002', 'product-003', 'product-004', 'product-005',
            'product-006', 'product-007', 'product-008', 'product-009', 'product-010'
        ]
        
        for product_id in expected_products:
            product_data = await mock_service.get_product(product_id)
            assert product_data is not None
            assert 'name' in product_data
            assert 'category' in product_data
            assert 'price' in product_data
    
    def test_add_product(self, mock_service):
        """Test adding a new product to the mock service."""
        mock_service.add_product(
            product_id='test-product',
            name='Test Product',
            category='Test Category',
            price=123.45
        )
        
        # Verify the product was added
        assert 'test-product' in mock_service.products
        product_data = mock_service.products['test-product']
        
        assert product_data['name'] == 'Test Product'
        assert product_data['category'] == 'Test Category'
        assert product_data['price'] == 123.45
    
    @pytest.mark.asyncio
    async def test_get_added_product(self, mock_service):
        """Test getting a product that was added dynamically."""
        # Add a product
        mock_service.add_product(
            product_id='dynamic-product',
            name='Dynamic Product',
            category='Dynamic Category',
            price=456.78
        )
        
        # Get the product
        product_data = await mock_service.get_product('dynamic-product')
        
        assert product_data is not None
        assert product_data['name'] == 'Dynamic Product'
        assert product_data['category'] == 'Dynamic Category'
        assert product_data['price'] == 456.78
    
    @pytest.mark.asyncio
    async def test_service_latency(self, mock_service):
        """Test that the mock service simulates realistic latency."""
        start_time = time.time()
        
        await mock_service.get_product('product-001')
        
        elapsed_time = time.time() - start_time
        
        # Should have some delay (at least 5ms as configured)
        assert elapsed_time >= 0.005
        assert elapsed_time < 0.1  # But not too much delay
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mock_service):
        """Test handling concurrent requests to the mock service."""
        # Create multiple concurrent requests
        tasks = [
            mock_service.get_product(f'product-{i:03d}')
            for i in range(1, 6)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        elapsed_time = time.time() - start_time
        
        # All requests should complete
        assert len(results) == 5
        
        # Should have some products found and some not found
        found_products = [r for r in results if r is not None]
        assert len(found_products) == 5  # All should be found (product-001 to product-005)
        
        # Concurrent requests should be faster than sequential
        assert elapsed_time < 0.1  # Should complete quickly due to concurrency


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
