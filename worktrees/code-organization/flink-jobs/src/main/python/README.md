# Python Order Enrichment Implementation

This directory contains the Python implementation of the order enrichment pipeline using PyFlink, providing a parallel implementation to the Java version.

## 🏗️ Architecture

The Python implementation follows the same architectural patterns as the Java version:

```
src/main/python/
├── com/goodrx/flink/enrichment/
│   ├── model/                          # Data models
│   │   ├── order_event.py             # OrderEvent model
│   │   ├── enriched_order_event.py    # EnrichedOrderEvent model
│   │   └── __init__.py                # Package exports
│   ├── functions/                      # Processing functions
│   │   ├── json_parser.py             # JSON parsing functions
│   │   ├── product_enrichment.py      # Product enrichment functions
│   │   └── __init__.py                # Package exports
│   ├── order_grpc_enrichment_job.py   # Main job implementation
│   └── __init__.py                    # Package exports
└── requirements.txt                    # Python dependencies
```

## 📊 Data Models

### OrderEvent
Represents a raw order event from Kafka:
```python
@dataclass
class OrderEvent:
    order_id: str
    customer_id: str
    product_id: str
    quantity: int
    price: float
    event_timestamp: int
```

**Features:**
- JSON serialization/deserialization
- Type validation and conversion
- Immutable dataclass design
- Rich string representations

### EnrichedOrderEvent
Represents an order event enriched with additional data:
```python
@dataclass
class EnrichedOrderEvent:
    order_event: OrderEvent
    product_name: Optional[str] = None
    product_category: Optional[str] = None
    product_price: Optional[float] = None
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    customer_tier: Optional[str] = None
    enrichment_timestamp: Optional[int] = None
```

**Features:**
- Fluent API for enrichment: `event.with_product_enrichment(...).with_customer_enrichment(...)`
- Enrichment status checking: `event.is_fully_enriched()`
- Immutable enrichment operations
- Selective serialization (only includes non-None fields)

## 🔧 Processing Functions

### JsonToOrderEventParser
Parses JSON strings into OrderEvent objects with error handling:
```python
class JsonToOrderEventParser(ProcessFunction):
    def process_element(self, value: str, ctx: ProcessFunction.Context):
        # Parses JSON and yields OrderEvent
        # Sends malformed records to side output
```

**Features:**
- Robust error handling for malformed JSON
- Type validation and conversion
- Side output for malformed records
- Performance statistics tracking

### ProductEnrichmentFunction
Enriches orders with product data via async gRPC calls:
```python
class ProductEnrichmentFunction(AsyncFunction):
    async def async_invoke(self, order_event: OrderEvent, result_future):
        # Fetches product data asynchronously
        # Enriches order with product information
```

**Features:**
- Async/await pattern for non-blocking I/O
- gRPC client integration
- Timeout handling
- Connection pooling support
- Graceful degradation on service failures

## 🚀 Performance Characteristics

Based on integration testing:

| Operation | Throughput | Notes |
|-----------|------------|-------|
| JSON Parsing | 275k events/sec | Including validation |
| Enrichment | 751k events/sec | With mock services |
| Serialization | 313k events/sec | JSON output |
| **End-to-End** | **122k events/sec** | Complete pipeline |

## 🧪 Testing

### Unit Tests
```bash
# Run model tests
python3 simple_test.py

# Expected output:
# 🎉 All tests passed! The Python order enrichment models are working correctly.
```

### Integration Tests
```bash
# Run integration tests
python3 integration_test.py

# Expected output:
# 🎉 All integration tests passed!
# ✅ The Python order enrichment pipeline is working correctly!
```

**Test Coverage:**
- ✅ Model serialization/deserialization
- ✅ Error handling and validation
- ✅ End-to-end pipeline simulation
- ✅ Performance characteristics
- ✅ Enrichment workflows
- ✅ JSON parsing with malformed data

## 📦 Dependencies

```bash
# Install PyFlink and dependencies
pip install -r requirements.txt
```

**Key Dependencies:**
- `apache-flink==1.18.1` - PyFlink runtime
- `grpcio==1.60.0` - gRPC client
- `protobuf==4.25.1` - Protocol buffers
- `pydantic==2.5.0` - Data validation
- `structlog==23.2.0` - Structured logging

## 🔧 Configuration

The job supports the same configuration options as the Java version:

```python
# Command line arguments
python order_grpc_enrichment_job.py \
    --kafka.source.bootstrap.servers localhost:9092 \
    --kafka.source.topic raw_orders \
    --kafka.sink.topic enriched_orders \
    --product.service.host localhost \
    --product.service.port 50051 \
    --job.parallelism 4 \
    --checkpoint.interval.ms 60000
```

## 🐳 Docker Integration

The Python implementation integrates with the existing Docker setup:

```dockerfile
# In Dockerfile, add Python support
COPY src/main/python/ /opt/flink/python/
ENV PYTHONPATH=/opt/flink/python
```

## ☸️ Kubernetes Deployment

Deploy alongside Java jobs using the same Kubernetes manifests:

```yaml
# In job deployment
spec:
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    entryClass: com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob
    # OR for Python
    # pythonScript: /opt/flink/python/com/goodrx/flink/enrichment/order_grpc_enrichment_job.py
```

## 🔍 Monitoring

The Python implementation provides the same observability as Java:

- **Metrics**: Flink's built-in metrics system
- **Logging**: Structured logging with correlation IDs
- **Checkpointing**: S3-backed state management
- **Health Checks**: Readiness and liveness probes

## 🤝 Development Guidelines

### Code Style
- Follow PEP 8 style guidelines
- Use type hints for all public APIs
- Document all classes and methods
- Prefer dataclasses for immutable data

### Error Handling
- Use specific exception types
- Log errors with context
- Implement graceful degradation
- Send malformed data to side outputs

### Performance
- Use async/await for I/O operations
- Implement connection pooling
- Cache frequently accessed data
- Monitor memory usage

### Testing
- Write unit tests for all models
- Create integration tests for pipelines
- Test error conditions
- Verify performance characteristics

## 🔄 Migration from Java

The Python implementation maintains API compatibility:

| Java Class | Python Equivalent | Notes |
|------------|------------------|-------|
| `OrderEvent` | `OrderEvent` | Same fields, dataclass |
| `EnrichedOrderEvent` | `EnrichedOrderEvent` | Fluent API added |
| `JsonToOrderEventParser` | `JsonToOrderEventParser` | Same interface |
| `ProductEnrichmentFunction` | `ProductEnrichmentFunction` | Async version |

## 📝 Future Enhancements

- [ ] Customer enrichment function
- [ ] Metrics collection integration
- [ ] Advanced error recovery
- [ ] Schema evolution support
- [ ] Performance optimizations
- [ ] Additional data sources

## 🆘 Troubleshooting

### Common Issues

**Import Errors:**
```bash
# Ensure PYTHONPATH is set
export PYTHONPATH=/path/to/src/main/python
```

**PyFlink Not Found:**
```bash
# Install PyFlink
pip install apache-flink==1.18.1
```

**gRPC Connection Issues:**
```bash
# Check service connectivity
telnet product-service-host 50051
```

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)
```
