"""
OrderEvent model for the Python Flink enrichment job.
"""

from typing import Dict, Any
from dataclasses import dataclass
import json


@dataclass
class OrderEvent:
    """
    Python equivalent of the Java OrderEvent model.
    
    Represents an order event from the Kafka stream containing basic order information
    that needs to be enriched with additional data.
    """
    
    order_id: str
    customer_id: str
    product_id: str
    quantity: int
    price: float
    event_timestamp: int
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderEvent':
        """
        Create OrderEvent from dictionary (parsed JSON).
        
        Args:
            data: Dictionary containing order event data
            
        Returns:
            OrderEvent instance
            
        Raises:
            KeyError: If required fields are missing
            TypeError: If field types are incorrect
        """
        return cls(
            order_id=str(data['orderId']),
            customer_id=str(data['customerId']),
            product_id=str(data['productId']),
            quantity=int(data['quantity']),
            price=float(data['price']),
            event_timestamp=int(data.get('eventTimestamp', 0))
        )
    
    @classmethod
    def from_json(cls, json_string: str) -> 'OrderEvent':
        """
        Create OrderEvent from JSON string.
        
        Args:
            json_string: JSON string containing order event data
            
        Returns:
            OrderEvent instance
            
        Raises:
            json.JSONDecodeError: If JSON is malformed
            KeyError: If required fields are missing
            TypeError: If field types are incorrect
        """
        data = json.loads(json_string)
        return cls.from_dict(data)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert OrderEvent to dictionary for JSON serialization.
        
        Returns:
            Dictionary representation of the order event
        """
        return {
            'orderId': self.order_id,
            'customerId': self.customer_id,
            'productId': self.product_id,
            'quantity': self.quantity,
            'price': self.price,
            'eventTimestamp': self.event_timestamp
        }
    
    def to_json(self) -> str:
        """
        Convert OrderEvent to JSON string.
        
        Returns:
            JSON string representation of the order event
        """
        return json.dumps(self.to_dict())
    
    def __str__(self) -> str:
        """String representation of the OrderEvent."""
        return f"OrderEvent(order_id={self.order_id}, customer_id={self.customer_id}, product_id={self.product_id})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the OrderEvent."""
        return (f"OrderEvent(order_id='{self.order_id}', customer_id='{self.customer_id}', "
                f"product_id='{self.product_id}', quantity={self.quantity}, "
                f"price={self.price}, event_timestamp={self.event_timestamp})")
