"""
Product enrichment functions for the Python Flink enrichment job.
"""

import logging
import time
from typing import Iterator, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import asyncio

from pyflink.datastream.functions import AsyncFunction, RuntimeContext
from ..model import OrderEvent, EnrichedOrderEvent

logger = logging.getLogger(__name__)


class ProductEnrichmentFunction(AsyncFunction):
    """
    Async function to enrich OrderEvent with product data via gRPC.
    This is a simplified version - in practice, you'd use actual gRPC calls.
    """
    
    def __init__(self, product_service_host: str, product_service_port: int, use_tls: bool = False):
        """
        Initialize the product enrichment function.
        
        Args:
            product_service_host: Host of the product service
            product_service_port: Port of the product service
            use_tls: Whether to use TLS for gRPC connection
        """
        self.product_service_host = product_service_host
        self.product_service_port = product_service_port
        self.use_tls = use_tls
        self.grpc_client = None
        self.executor = None
        self.enrichment_count = 0
        self.error_count = 0
        
        # Mock product database for demonstration
        self.product_database = {
            'product-001': {'name': 'Wireless Headphones', 'category': 'Electronics', 'price': 99.99},
            'product-002': {'name': 'Coffee Mug', 'category': 'Home & Kitchen', 'price': 15.99},
            'product-003': {'name': 'Running Shoes', 'category': 'Sports & Outdoors', 'price': 129.99},
            'product-004': {'name': 'Smartphone', 'category': 'Electronics', 'price': 699.99},
            'product-005': {'name': 'Book: Python Programming', 'category': 'Books', 'price': 39.99},
        }
    
    def open(self, runtime_context: RuntimeContext):
        """Initialize gRPC client and thread pool."""
        logger.info(f"Initializing product enrichment with gRPC service at "
                   f"{self.product_service_host}:{self.product_service_port} (TLS: {self.use_tls})")
        
        # Initialize thread pool for async operations
        self.executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="product-enrichment")
        
        # In a real implementation, you would initialize the gRPC client here
        # self.grpc_client = ProductServiceClient(
        #     host=self.product_service_host,
        #     port=self.product_service_port,
        #     use_tls=self.use_tls
        # )
        
        self.enrichment_count = 0
        self.error_count = 0
        logger.info("Product enrichment function initialized successfully")
    
    async def async_invoke(self, order_event: OrderEvent, result_future) -> None:
        """
        Asynchronously enrich an OrderEvent with product data.
        
        Args:
            order_event: OrderEvent to enrich
            result_future: Future to complete with the result
        """
        try:
            # Simulate async gRPC call
            product_data = await self._fetch_product_data(order_event.product_id)
            
            if product_data:
                # Create enriched event with product data
                enriched_event = EnrichedOrderEvent.from_order_event(order_event)
                enriched_event = enriched_event.with_product_enrichment(
                    product_name=product_data['name'],
                    product_category=product_data['category'],
                    product_price=product_data.get('price')
                )
                enriched_event.enrichment_timestamp = int(time.time() * 1000)
                
                self.enrichment_count += 1
                
                # Log progress every 100 enrichments
                if self.enrichment_count % 100 == 0:
                    logger.info(f"Successfully enriched {self.enrichment_count} orders")
                
                result_future.complete([enriched_event])
            else:
                # Product not found, return order without enrichment
                logger.warning(f"Product not found for ID: {order_event.product_id}")
                enriched_event = EnrichedOrderEvent.from_order_event(order_event)
                result_future.complete([enriched_event])
                
        except Exception as e:
            self.error_count += 1
            logger.error(f"Failed to enrich order {order_event.order_id} with product data: {e}")
            
            # Return order without enrichment on error
            enriched_event = EnrichedOrderEvent.from_order_event(order_event)
            result_future.complete([enriched_event])
    
    async def _fetch_product_data(self, product_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch product data from the product service.
        
        Args:
            product_id: ID of the product to fetch
            
        Returns:
            Product data dictionary or None if not found
        """
        # Simulate network delay
        await asyncio.sleep(0.01)  # 10ms delay
        
        # In a real implementation, this would make a gRPC call
        # try:
        #     response = await self.grpc_client.get_product(product_id)
        #     return {
        #         'name': response.name,
        #         'category': response.category,
        #         'price': response.price
        #     }
        # except grpc.RpcError as e:
        #     logger.error(f"gRPC error fetching product {product_id}: {e}")
        #     return None
        
        # Mock implementation
        return self.product_database.get(product_id)
    
    def timeout(self, order_event: OrderEvent, result_future) -> None:
        """
        Handle timeout for async operation.
        
        Args:
            order_event: OrderEvent that timed out
            result_future: Future to complete with timeout result
        """
        logger.warning(f"Timeout enriching order {order_event.order_id} with product data")
        self.error_count += 1
        
        # Return order without enrichment on timeout
        enriched_event = EnrichedOrderEvent.from_order_event(order_event)
        result_future.complete([enriched_event])
    
    def close(self):
        """Cleanup resources when the function is closed."""
        logger.info(f"Product enrichment function closing. Stats - Enriched: {self.enrichment_count}, "
                   f"Errors: {self.error_count}")
        
        if self.executor:
            self.executor.shutdown(wait=True)
            logger.info("Thread pool executor shut down")
        
        if self.grpc_client:
            # In a real implementation, close the gRPC client
            # self.grpc_client.close()
            pass


class MockProductService:
    """
    Mock product service for testing and development.
    """
    
    def __init__(self):
        """Initialize the mock service with sample data."""
        self.products = {
            'product-001': {'name': 'Wireless Headphones', 'category': 'Electronics', 'price': 99.99},
            'product-002': {'name': 'Coffee Mug', 'category': 'Home & Kitchen', 'price': 15.99},
            'product-003': {'name': 'Running Shoes', 'category': 'Sports & Outdoors', 'price': 129.99},
            'product-004': {'name': 'Smartphone', 'category': 'Electronics', 'price': 699.99},
            'product-005': {'name': 'Book: Python Programming', 'category': 'Books', 'price': 39.99},
            'product-006': {'name': 'Gaming Mouse', 'category': 'Electronics', 'price': 79.99},
            'product-007': {'name': 'Water Bottle', 'category': 'Sports & Outdoors', 'price': 24.99},
            'product-008': {'name': 'Desk Lamp', 'category': 'Home & Kitchen', 'price': 45.99},
            'product-009': {'name': 'Bluetooth Speaker', 'category': 'Electronics', 'price': 149.99},
            'product-010': {'name': 'Notebook', 'category': 'Office Supplies', 'price': 12.99},
        }
    
    async def get_product(self, product_id: str) -> Optional[Dict[str, Any]]:
        """
        Get product information by ID.
        
        Args:
            product_id: ID of the product
            
        Returns:
            Product data or None if not found
        """
        # Simulate network delay
        await asyncio.sleep(0.005)  # 5ms delay
        return self.products.get(product_id)
    
    def add_product(self, product_id: str, name: str, category: str, price: float):
        """Add a new product to the mock service."""
        self.products[product_id] = {
            'name': name,
            'category': category,
            'price': price
        }
