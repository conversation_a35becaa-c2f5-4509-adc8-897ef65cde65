"""
JSON parsing functions for the Python Flink enrichment job.
"""

import json
import logging
from typing import Iterator

from pyflink.datastream.functions import ProcessFunction
from ..model import OrderEvent

logger = logging.getLogger(__name__)


class JsonToOrderEventParser(ProcessFunction):
    """
    ProcessFunction to parse JSON strings into OrderEvent objects.
    Malformed records are sent to a side output for error handling.
    """
    
    def __init__(self, malformed_records_tag: str):
        """
        Initialize the JSON parser.
        
        Args:
            malformed_records_tag: Tag for side output of malformed records
        """
        self.malformed_records_tag = malformed_records_tag
        self.successful_parses = 0
        self.failed_parses = 0
    
    def open(self, runtime_context):
        """Initialize the function when it starts."""
        logger.info("JsonToOrderEventParser opened")
        self.successful_parses = 0
        self.failed_parses = 0
    
    def process_element(self, value: str, ctx: ProcessFunction.Context) -> Iterator[OrderEvent]:
        """
        Process a JSON string and convert it to an OrderEvent.
        
        Args:
            value: JSON string to parse
            ctx: Process function context
            
        Yields:
            OrderEvent if parsing is successful
        """
        try:
            # Parse JSON string to OrderEvent
            order_event = OrderEvent.from_json(value)
            self.successful_parses += 1
            
            # Log every 100 successful parses
            if self.successful_parses % 100 == 0:
                logger.info(f"Successfully parsed {self.successful_parses} order events")
            
            yield order_event
            
        except (json.JSONDecodeError, KeyError, TypeError, ValueError) as e:
            self.failed_parses += 1
            logger.warning(f"Failed to parse JSON to OrderEvent: {e}. Record: {value[:100]}...")
            
            # Send malformed record to side output
            ctx.output(self.malformed_records_tag, value)
            
            # Log error statistics every 10 failures
            if self.failed_parses % 10 == 0:
                logger.warning(f"Total parsing failures: {self.failed_parses}")
    
    def close(self):
        """Cleanup when the function is closed."""
        logger.info(f"JsonToOrderEventParser closed. Stats - Successful: {self.successful_parses}, "
                   f"Failed: {self.failed_parses}")


class OrderEventValidator:
    """
    Utility class for validating OrderEvent data.
    """
    
    @staticmethod
    def validate_order_event(order_event: OrderEvent) -> bool:
        """
        Validate an OrderEvent for business rules.
        
        Args:
            order_event: OrderEvent to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields are not empty
            if not order_event.order_id or not order_event.customer_id or not order_event.product_id:
                return False
            
            # Check quantity is positive
            if order_event.quantity <= 0:
                return False
            
            # Check price is non-negative
            if order_event.price < 0:
                return False
            
            # Check timestamp is reasonable (not negative, not too far in future)
            if order_event.event_timestamp < 0:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating order event: {e}")
            return False
    
    @staticmethod
    def sanitize_order_event(order_event: OrderEvent) -> OrderEvent:
        """
        Sanitize an OrderEvent by cleaning up data.
        
        Args:
            order_event: OrderEvent to sanitize
            
        Returns:
            Sanitized OrderEvent
        """
        return OrderEvent(
            order_id=order_event.order_id.strip(),
            customer_id=order_event.customer_id.strip(),
            product_id=order_event.product_id.strip(),
            quantity=max(1, order_event.quantity),  # Ensure at least 1
            price=max(0.0, order_event.price),      # Ensure non-negative
            event_timestamp=order_event.event_timestamp
        )
