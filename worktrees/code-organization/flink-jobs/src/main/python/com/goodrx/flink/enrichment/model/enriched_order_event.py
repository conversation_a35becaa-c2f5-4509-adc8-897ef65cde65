"""
EnrichedOrderEvent model for the Python Flink enrichment job.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
import json

from .order_event import OrderEvent


@dataclass
class EnrichedOrderEvent:
    """
    Python equivalent of the Java EnrichedOrderEvent model.
    
    Represents an order event that has been enriched with additional information
    from external services (product details, customer information, etc.).
    """
    
    order_event: OrderEvent
    product_name: Optional[str] = None
    product_category: Optional[str] = None
    product_price: Optional[float] = None
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    customer_tier: Optional[str] = None
    enrichment_timestamp: Optional[int] = None
    
    @classmethod
    def from_order_event(cls, order_event: OrderEvent) -> 'EnrichedOrderEvent':
        """
        Create an EnrichedOrderEvent from an OrderEvent with no enrichment data.
        
        Args:
            order_event: The base order event to enrich
            
        Returns:
            EnrichedOrderEvent instance with only base order data
        """
        return cls(order_event=order_event)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnrichedOrderEvent':
        """
        Create EnrichedOrderEvent from dictionary.
        
        Args:
            data: Dictionary containing enriched order event data
            
        Returns:
            EnrichedOrderEvent instance
        """
        # Extract base order event data
        order_data = {
            'orderId': data['orderId'],
            'customerId': data['customerId'],
            'productId': data['productId'],
            'quantity': data['quantity'],
            'price': data['price'],
            'eventTimestamp': data.get('eventTimestamp', 0)
        }
        order_event = OrderEvent.from_dict(order_data)
        
        # Create enriched event with additional data
        return cls(
            order_event=order_event,
            product_name=data.get('productName'),
            product_category=data.get('productCategory'),
            product_price=data.get('productPrice'),
            customer_name=data.get('customerName'),
            customer_email=data.get('customerEmail'),
            customer_tier=data.get('customerTier'),
            enrichment_timestamp=data.get('enrichmentTimestamp')
        )
    
    @classmethod
    def from_json(cls, json_string: str) -> 'EnrichedOrderEvent':
        """
        Create EnrichedOrderEvent from JSON string.
        
        Args:
            json_string: JSON string containing enriched order event data
            
        Returns:
            EnrichedOrderEvent instance
        """
        data = json.loads(json_string)
        return cls.from_dict(data)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert EnrichedOrderEvent to dictionary for JSON serialization.
        
        Returns:
            Dictionary representation of the enriched order event
        """
        result = self.order_event.to_dict()
        
        # Add enrichment data if present
        enrichment_fields = {
            'productName': self.product_name,
            'productCategory': self.product_category,
            'productPrice': self.product_price,
            'customerName': self.customer_name,
            'customerEmail': self.customer_email,
            'customerTier': self.customer_tier,
            'enrichmentTimestamp': self.enrichment_timestamp
        }
        
        # Only include non-None values
        for key, value in enrichment_fields.items():
            if value is not None:
                result[key] = value
        
        return result
    
    def to_json(self) -> str:
        """
        Convert EnrichedOrderEvent to JSON string.
        
        Returns:
            JSON string representation of the enriched order event
        """
        return json.dumps(self.to_dict())
    
    def with_product_enrichment(self, product_name: str, product_category: str, 
                               product_price: float = None) -> 'EnrichedOrderEvent':
        """
        Create a new EnrichedOrderEvent with product enrichment data.
        
        Args:
            product_name: Name of the product
            product_category: Category of the product
            product_price: Price of the product (optional)
            
        Returns:
            New EnrichedOrderEvent instance with product data
        """
        return EnrichedOrderEvent(
            order_event=self.order_event,
            product_name=product_name,
            product_category=product_category,
            product_price=product_price,
            customer_name=self.customer_name,
            customer_email=self.customer_email,
            customer_tier=self.customer_tier,
            enrichment_timestamp=self.enrichment_timestamp
        )
    
    def with_customer_enrichment(self, customer_name: str, customer_email: str = None, 
                                customer_tier: str = None) -> 'EnrichedOrderEvent':
        """
        Create a new EnrichedOrderEvent with customer enrichment data.
        
        Args:
            customer_name: Name of the customer
            customer_email: Email of the customer (optional)
            customer_tier: Tier/level of the customer (optional)
            
        Returns:
            New EnrichedOrderEvent instance with customer data
        """
        return EnrichedOrderEvent(
            order_event=self.order_event,
            product_name=self.product_name,
            product_category=self.product_category,
            product_price=self.product_price,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_tier=customer_tier,
            enrichment_timestamp=self.enrichment_timestamp
        )
    
    def is_fully_enriched(self) -> bool:
        """
        Check if the event has been fully enriched with both product and customer data.
        
        Returns:
            True if both product and customer data are present
        """
        return (self.product_name is not None and 
                self.customer_name is not None)
    
    def __str__(self) -> str:
        """String representation of the EnrichedOrderEvent."""
        return (f"EnrichedOrderEvent(order_id={self.order_event.order_id}, "
                f"product_name={self.product_name}, customer_name={self.customer_name})")
    
    def __repr__(self) -> str:
        """Detailed string representation of the EnrichedOrderEvent."""
        return (f"EnrichedOrderEvent(order_event={repr(self.order_event)}, "
                f"product_name='{self.product_name}', product_category='{self.product_category}', "
                f"customer_name='{self.customer_name}', customer_tier='{self.customer_tier}')")
