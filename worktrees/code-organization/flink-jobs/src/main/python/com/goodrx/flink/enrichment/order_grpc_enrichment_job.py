"""
A PyFlink streaming job that enriches incoming order events with additional information
by making asynchronous calls to external gRPC services (e.g., Product Service, Customer Service).

This is the Python equivalent of the Java OrderGrpcEnrichmentJob.

The job performs the following key steps:
1. Consumes raw order messages (expected as JSON strings) from a Kafka topic.
2. Parses these JSON strings into OrderEvent objects.
   Malformed records are routed to a Dead Letter Queue (DLQ) side output.
3. Assigns timestamps and watermarks to the stream of valid OrderEvents for event time processing.
4. Enriches each OrderEvent by asynchronously calling a Product gRPC service to fetch product details.
5. Sinks the final enriched order events to an output Kafka topic.

Configuration:
The job is configured using command-line arguments or environment variables.
Key configurable parameters include Kafka broker details, topic names, gRPC service endpoints,
checkpointing settings, and job parallelism.

Example execution:
python order_grpc_enrichment_job.py \
    --kafka.source.bootstrap.servers localhost:9092 \
    --kafka.source.topic raw_orders \
    --kafka.sink.topic enriched_orders \
    --product.service.host localhost \
    --product.service.port 50051
"""

import logging
import sys
from typing import Dict, Any

from pyflink.common import WatermarkStrategy, Time
from pyflink.common.serialization import SimpleStringSchema
from pyflink.common.typeinfo import Types
from pyflink.datastream import StreamExecutionEnvironment, DataStream
from pyflink.datastream.connectors import FlinkKafkaConsumer, FlinkKafkaProducer

# Import our separated model and function classes
from .model import OrderEvent, EnrichedOrderEvent
from .functions import JsonToOrderEventParser, ProductEnrichmentFunction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def parse_arguments() -> Dict[str, str]:
    """Parse command-line arguments into a configuration dictionary."""
    config = {}
    args = sys.argv[1:]
    
    i = 0
    while i < len(args):
        if args[i].startswith('--'):
            key = args[i][2:]  # Remove '--' prefix
            if i + 1 < len(args) and not args[i + 1].startswith('--'):
                config[key] = args[i + 1]
                i += 2
            else:
                config[key] = 'true'  # Boolean flag
                i += 1
        else:
            i += 1
    
    return config


def create_execution_environment() -> StreamExecutionEnvironment:
    """Create and return a new StreamExecutionEnvironment."""
    return StreamExecutionEnvironment.get_execution_environment()


def configure_environment(env: StreamExecutionEnvironment, config: Dict[str, str]):
    """Configure the Flink environment with checkpointing and parallelism."""
    # Configure checkpointing
    checkpoint_interval = int(config.get('checkpoint.interval.ms', '60000'))
    if checkpoint_interval > 0:
        logger.info(f"Enabling checkpointing with interval: {checkpoint_interval} ms")
        env.enable_checkpointing(checkpoint_interval)
        # Additional checkpoint configuration would go here
    
    # Set parallelism
    if 'job.parallelism' in config:
        parallelism = int(config['job.parallelism'])
        env.set_parallelism(parallelism)
        logger.info(f"Job parallelism set to: {parallelism}")


def main():
    """Main entry point for the PyFlink job."""
    logger.info("Initializing Order Enrichment PyFlink Job...")
    
    # Parse configuration
    config = parse_arguments()
    
    # Create and configure environment
    env = create_execution_environment()
    configure_environment(env, config)
    
    # TODO: Implement the rest of the job pipeline
    # This would include:
    # 1. Creating Kafka source
    # 2. Processing raw order stream
    # 3. Enriching with product data
    # 4. Sinking to Kafka
    
    logger.info("PyFlink job setup complete - implementation to be continued...")


if __name__ == "__main__":
    main()
