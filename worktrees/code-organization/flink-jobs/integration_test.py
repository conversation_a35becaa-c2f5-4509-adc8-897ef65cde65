#!/usr/bin/env python3
"""
Integration test for Python order enrichment pipeline.
Simulates the complete end-to-end flow without requiring PyFlink.
"""

import sys
import os
import json
import time
import asyncio
import traceback
from typing import List, Dict, Any

# Add the source directory to Python path
sys.path.insert(0, 'src/main/python')

# Mock classes to simulate PyFlink functionality
class MockProcessFunctionContext:
    """Mock context for ProcessFunction testing."""
    
    def __init__(self):
        self.side_outputs = {}
    
    def output(self, tag: str, value: Any):
        """Mock side output functionality."""
        if tag not in self.side_outputs:
            self.side_outputs[tag] = []
        self.side_outputs[tag].append(value)


class MockRuntimeContext:
    """Mock runtime context for function testing."""
    pass


def test_json_parsing_pipeline():
    """Test the JSON parsing pipeline with realistic data."""
    print("🧪 Testing JSON parsing pipeline...")
    
    try:
        from com.goodrx.flink.enrichment.model import OrderEvent
        
        # Sample order events (realistic e-commerce data)
        sample_orders = [
            {
                'orderId': 'ORD-2024-001',
                'customerId': 'CUST-12345',
                'productId': 'PROD-HEADPHONES-001',
                'quantity': 2,
                'price': 199.99,
                'eventTimestamp': int(time.time() * 1000)
            },
            {
                'orderId': 'ORD-2024-002',
                'customerId': 'CUST-67890',
                'productId': 'PROD-SMARTPHONE-002',
                'quantity': 1,
                'price': 899.99,
                'eventTimestamp': int(time.time() * 1000)
            },
            {
                'orderId': 'ORD-2024-003',
                'customerId': 'CUST-11111',
                'productId': 'PROD-BOOK-003',
                'quantity': 3,
                'price': 29.99,
                'eventTimestamp': int(time.time() * 1000)
            }
        ]
        
        # Malformed records to test error handling
        malformed_records = [
            '{"orderId": "ORD-BAD-001", "missing": "required_fields"}',
            'not json at all',
            '{"orderId": "", "customerId": "", "productId": ""}',  # Empty fields
            '{"orderId": "ORD-BAD-002", "quantity": "not_a_number"}'
        ]
        
        # Simulate JSON parsing
        parsed_events = []
        malformed_count = 0
        
        print("  📝 Processing valid order events...")
        for order_data in sample_orders:
            try:
                json_string = json.dumps(order_data)
                order_event = OrderEvent.from_json(json_string)
                parsed_events.append(order_event)
                print(f"    ✅ Parsed: {order_event.order_id}")
            except Exception as e:
                print(f"    ❌ Failed to parse valid order: {e}")
                return False
        
        print("  📝 Processing malformed records...")
        for malformed_record in malformed_records:
            try:
                OrderEvent.from_json(malformed_record)
                print(f"    ❌ Should have failed: {malformed_record[:50]}...")
                return False
            except Exception:
                malformed_count += 1
                print(f"    ✅ Correctly rejected malformed record")
        
        # Verify results
        assert len(parsed_events) == len(sample_orders)
        assert malformed_count == len(malformed_records)
        
        print(f"  📊 Results: {len(parsed_events)} valid, {malformed_count} malformed")
        print("🎉 JSON parsing pipeline test passed!")
        return parsed_events
        
    except Exception as e:
        print(f"  ❌ JSON parsing pipeline test failed: {e}")
        traceback.print_exc()
        return None


def test_product_enrichment_pipeline(order_events: List):
    """Test product enrichment with mock product service."""
    print("\n🧪 Testing product enrichment pipeline...")
    
    try:
        from com.goodrx.flink.enrichment.model import EnrichedOrderEvent
        
        # Mock product database (simulating external service)
        product_database = {
            'PROD-HEADPHONES-001': {
                'name': 'Wireless Noise-Canceling Headphones',
                'category': 'Electronics',
                'price': 199.99,
                'brand': 'AudioTech',
                'rating': 4.5
            },
            'PROD-SMARTPHONE-002': {
                'name': 'Flagship Smartphone 128GB',
                'category': 'Electronics',
                'price': 899.99,
                'brand': 'TechCorp',
                'rating': 4.8
            },
            'PROD-BOOK-003': {
                'name': 'Python Programming Mastery',
                'category': 'Books',
                'price': 29.99,
                'brand': 'TechBooks',
                'rating': 4.7
            }
        }
        
        enriched_events = []
        
        print("  🔍 Enriching orders with product data...")
        for order_event in order_events:
            # Simulate product lookup
            product_data = product_database.get(order_event.product_id)
            
            if product_data:
                enriched_event = (EnrichedOrderEvent.from_order_event(order_event)
                                .with_product_enrichment(
                                    product_name=product_data['name'],
                                    product_category=product_data['category'],
                                    product_price=product_data['price']
                                ))
                enriched_event.enrichment_timestamp = int(time.time() * 1000)
                enriched_events.append(enriched_event)
                print(f"    ✅ Enriched: {order_event.order_id} -> {product_data['name']}")
            else:
                # Product not found, keep original order
                enriched_event = EnrichedOrderEvent.from_order_event(order_event)
                enriched_events.append(enriched_event)
                print(f"    ⚠️  Product not found for: {order_event.product_id}")
        
        # Verify enrichment
        assert len(enriched_events) == len(order_events)
        
        enriched_count = sum(1 for e in enriched_events if e.product_name is not None)
        print(f"  📊 Results: {enriched_count}/{len(enriched_events)} orders enriched")
        
        print("🎉 Product enrichment pipeline test passed!")
        return enriched_events
        
    except Exception as e:
        print(f"  ❌ Product enrichment pipeline test failed: {e}")
        traceback.print_exc()
        return None


def test_customer_enrichment_pipeline(enriched_events: List):
    """Test customer enrichment with mock customer service."""
    print("\n🧪 Testing customer enrichment pipeline...")
    
    try:
        # Mock customer database
        customer_database = {
            'CUST-12345': {
                'name': 'Alice Johnson',
                'email': '<EMAIL>',
                'tier': 'Gold',
                'join_date': '2022-01-15',
                'total_orders': 47
            },
            'CUST-67890': {
                'name': 'Bob Smith',
                'email': '<EMAIL>',
                'tier': 'Platinum',
                'join_date': '2021-06-20',
                'total_orders': 123
            },
            'CUST-11111': {
                'name': 'Carol Davis',
                'email': '<EMAIL>',
                'tier': 'Silver',
                'join_date': '2023-03-10',
                'total_orders': 12
            }
        }
        
        fully_enriched_events = []
        
        print("  👤 Enriching orders with customer data...")
        for enriched_event in enriched_events:
            customer_data = customer_database.get(enriched_event.order_event.customer_id)
            
            if customer_data:
                fully_enriched = enriched_event.with_customer_enrichment(
                    customer_name=customer_data['name'],
                    customer_email=customer_data['email'],
                    customer_tier=customer_data['tier']
                )
                fully_enriched_events.append(fully_enriched)
                print(f"    ✅ Customer enriched: {enriched_event.order_event.customer_id} -> {customer_data['name']} ({customer_data['tier']})")
            else:
                # Customer not found, keep existing enrichment
                fully_enriched_events.append(enriched_event)
                print(f"    ⚠️  Customer not found: {enriched_event.order_event.customer_id}")
        
        # Verify enrichment
        assert len(fully_enriched_events) == len(enriched_events)
        
        fully_enriched_count = sum(1 for e in fully_enriched_events if e.is_fully_enriched())
        print(f"  📊 Results: {fully_enriched_count}/{len(fully_enriched_events)} orders fully enriched")
        
        print("🎉 Customer enrichment pipeline test passed!")
        return fully_enriched_events
        
    except Exception as e:
        print(f"  ❌ Customer enrichment pipeline test failed: {e}")
        traceback.print_exc()
        return None


def test_output_serialization(final_events: List):
    """Test serialization of final enriched events."""
    print("\n🧪 Testing output serialization...")
    
    try:
        serialized_events = []
        
        print("  📤 Serializing enriched events...")
        for event in final_events:
            # Test JSON serialization
            json_string = event.to_json()
            
            # Verify we can parse it back
            parsed_dict = json.loads(json_string)
            
            # Verify required fields are present
            required_fields = ['orderId', 'customerId', 'productId', 'quantity', 'price']
            for field in required_fields:
                assert field in parsed_dict, f"Missing required field: {field}"
            
            serialized_events.append(json_string)
            
            # Log enrichment status
            enrichment_info = []
            if event.product_name:
                enrichment_info.append(f"Product: {event.product_name}")
            if event.customer_name:
                enrichment_info.append(f"Customer: {event.customer_name}")
            
            enrichment_str = ", ".join(enrichment_info) if enrichment_info else "No enrichment"
            print(f"    ✅ Serialized: {event.order_event.order_id} ({enrichment_str})")
        
        assert len(serialized_events) == len(final_events)
        
        # Test batch serialization performance
        start_time = time.time()
        batch_json = json.dumps([event.to_dict() for event in final_events])
        elapsed = time.time() - start_time
        
        print(f"  📊 Batch serialization: {len(final_events)} events in {elapsed:.3f}s")
        print(f"  📊 Output size: {len(batch_json)} characters")
        
        print("🎉 Output serialization test passed!")
        return serialized_events
        
    except Exception as e:
        print(f"  ❌ Output serialization test failed: {e}")
        traceback.print_exc()
        return None


def test_end_to_end_pipeline():
    """Test the complete end-to-end enrichment pipeline."""
    print("\n🚀 Testing complete end-to-end pipeline...")
    
    try:
        # Step 1: JSON Parsing
        parsed_events = test_json_parsing_pipeline()
        if not parsed_events:
            return False
        
        # Step 2: Product Enrichment
        product_enriched = test_product_enrichment_pipeline(parsed_events)
        if not product_enriched:
            return False
        
        # Step 3: Customer Enrichment
        fully_enriched = test_customer_enrichment_pipeline(product_enriched)
        if not fully_enriched:
            return False
        
        # Step 4: Output Serialization
        serialized_output = test_output_serialization(fully_enriched)
        if not serialized_output:
            return False
        
        # Final verification
        print("\n📊 End-to-End Pipeline Summary:")
        print(f"  • Input events processed: {len(parsed_events)}")
        print(f"  • Product enrichments: {sum(1 for e in fully_enriched if e.product_name)}")
        print(f"  • Customer enrichments: {sum(1 for e in fully_enriched if e.customer_name)}")
        print(f"  • Fully enriched events: {sum(1 for e in fully_enriched if e.is_fully_enriched())}")
        print(f"  • Output events: {len(serialized_output)}")
        
        print("🎉 End-to-end pipeline test passed!")
        return True
        
    except Exception as e:
        print(f"❌ End-to-end pipeline test failed: {e}")
        traceback.print_exc()
        return False


def test_performance_characteristics():
    """Test performance characteristics of the pipeline."""
    print("\n🧪 Testing performance characteristics...")
    
    try:
        from com.goodrx.flink.enrichment.model import OrderEvent, EnrichedOrderEvent
        
        # Generate a larger dataset
        print("  📈 Generating large dataset...")
        large_dataset = []
        for i in range(1000):
            order_data = {
                'orderId': f'ORD-PERF-{i:06d}',
                'customerId': f'CUST-{i % 100:05d}',  # 100 unique customers
                'productId': f'PROD-{i % 50:03d}',    # 50 unique products
                'quantity': (i % 5) + 1,
                'price': round((i % 1000) + 10.99, 2),
                'eventTimestamp': int(time.time() * 1000) + i
            }
            large_dataset.append(order_data)
        
        # Test parsing performance
        start_time = time.time()
        parsed_events = []
        for order_data in large_dataset:
            json_string = json.dumps(order_data)
            order_event = OrderEvent.from_json(json_string)
            parsed_events.append(order_event)
        parsing_time = time.time() - start_time
        
        # Test enrichment performance
        start_time = time.time()
        enriched_events = []
        for order_event in parsed_events:
            enriched = (EnrichedOrderEvent.from_order_event(order_event)
                       .with_product_enrichment(f'Product {order_event.product_id}', 'Category')
                       .with_customer_enrichment(f'Customer {order_event.customer_id}', '<EMAIL>', 'Gold'))
            enriched_events.append(enriched)
        enrichment_time = time.time() - start_time
        
        # Test serialization performance
        start_time = time.time()
        json_outputs = [event.to_json() for event in enriched_events]
        serialization_time = time.time() - start_time
        
        total_time = parsing_time + enrichment_time + serialization_time
        
        print(f"  📊 Performance Results for {len(large_dataset)} events:")
        print(f"    • Parsing: {parsing_time:.3f}s ({len(large_dataset)/parsing_time:.0f} events/sec)")
        print(f"    • Enrichment: {enrichment_time:.3f}s ({len(large_dataset)/enrichment_time:.0f} events/sec)")
        print(f"    • Serialization: {serialization_time:.3f}s ({len(large_dataset)/serialization_time:.0f} events/sec)")
        print(f"    • Total: {total_time:.3f}s ({len(large_dataset)/total_time:.0f} events/sec)")
        
        # Performance assertions
        assert parsing_time < 1.0, f"Parsing too slow: {parsing_time:.3f}s"
        assert enrichment_time < 1.0, f"Enrichment too slow: {enrichment_time:.3f}s"
        assert serialization_time < 1.0, f"Serialization too slow: {serialization_time:.3f}s"
        assert total_time < 3.0, f"Total processing too slow: {total_time:.3f}s"
        
        print("🎉 Performance test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all integration tests."""
    print("🚀 Python Order Enrichment Integration Tests")
    print("=" * 60)
    
    tests = [
        test_end_to_end_pipeline,
        test_performance_characteristics
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            traceback.print_exc()
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Integration Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All integration tests passed!")
        print("✅ The Python order enrichment pipeline is working correctly!")
        print("✅ Ready for production deployment with PyFlink!")
        return 0
    else:
        print("❌ Some integration tests failed. Please check the output above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
