#!/usr/bin/env python3
"""
Simple test script to debug import issues.
"""

import sys
import os

# Add the source directory to Python path
sys.path.insert(0, 'src/main/python')

print("Python path:")
for path in sys.path:
    print(f"  {path}")

print("\nTesting imports...")

try:
    print("1. Testing basic import...")
    from com.goodrx.flink.enrichment.model.order_event import OrderEvent
    print("✅ OrderEvent import successful")
    
    print("2. Testing OrderEvent creation...")
    order = OrderEvent(
        order_id='test-order',
        customer_id='test-customer', 
        product_id='test-product',
        quantity=1,
        price=10.0,
        event_timestamp=12345
    )
    print("✅ OrderEvent creation successful")
    print(f"Order: {order}")
    
    print("3. Testing model package import...")
    from com.goodrx.flink.enrichment.model import OrderEvent as OrderEvent2
    print("✅ Model package import successful")
    
    print("4. Testing EnrichedOrderEvent import...")
    from com.goodrx.flink.enrichment.model import EnrichedOrderEvent
    print("✅ EnrichedOrderEvent import successful")
    
    print("5. Testing functions import...")
    from com.goodrx.flink.enrichment.functions import JsonToOrderEventParser
    print("✅ Functions import successful")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
