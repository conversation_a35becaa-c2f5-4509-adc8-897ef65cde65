# Development dependencies for Python order enrichment
# This file contains only the dependencies needed for development and testing
# without the full PyFlink stack which has complex dependency conflicts

# Testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Data validation and serialization
pydantic>=2.5.0

# gRPC for service communication (without version conflicts)
grpcio>=1.50.0
grpcio-tools>=1.50.0
protobuf>=3.20.0,<5.0.0

# JSON and data processing
orjson>=3.8.0

# Async support
asyncio-mqtt>=0.13.0

# Logging
structlog>=23.1.0

# Development tools
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
